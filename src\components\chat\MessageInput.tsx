"use client";
import React, { useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { addMessage, fetchMessages, setUserTyping } from '@/store/slices/messageSlice';
import socketService from '@/lib/socket';
import { debounce } from 'lodash';
import encryptionService from '@/lib/encryptionService';
import { Button } from '@/components/ui/button';
import { Send } from 'lucide-react';

interface MessageInputProps {
  channelId: string;
}

const MessageInput: React.FC<MessageInputProps> = ({ channelId }) => {
  const [message, setMessage] = useState('');
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  const sendMessage = async () => {
    if (!message.trim()) return;

    try {
      // For now, send plain text messages to avoid encryption issues
      // TODO: Re-enable encryption once key management is properly implemented
      const messageData = {
        content: message.trim(),
        channelId,
        messageType: 'TEXT',
      };

      // Send via Socket.IO - the server will broadcast it back to all clients including us
      socketService.sendMessage(messageData);
      setMessage('');

      // Stop typing indicator when message is sent
      socketService.stopTyping(channelId, user?.username || '');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleTyping = debounce(() => {
    socketService.stopTyping(channelId, user?.username || '');
  }, 1000);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
    socketService.startTyping(channelId, user?.username || '');
    handleTyping();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="px-6 py-4 border-t border-gray-200 bg-white">
      <div className="flex items-center space-x-3">
        <input
          type="text"
          className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
          placeholder="Type your message..."
          value={message}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
        />
        <Button
          onClick={sendMessage}
          disabled={!message.trim()}
          size="sm"
          className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default MessageInput;

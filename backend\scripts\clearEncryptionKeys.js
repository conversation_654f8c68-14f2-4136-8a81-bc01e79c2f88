// This script should be run in the browser console to clear all encryption keys
// Run this when you encounter encryption/decryption errors after database cleanup

(function clearAllEncryptionData() {
  console.log('🧹 Clearing all encryption data...');
  
  // Clear all localStorage keys related to encryption
  const keys = Object.keys(localStorage);
  let clearedCount = 0;
  
  keys.forEach(key => {
    if (key.includes('whisperwave_private_key_') || 
        key.includes('encryption_') || 
        key.includes('crypto_')) {
      localStorage.removeItem(key);
      clearedCount++;
      console.log(`🗑️ Cleared: ${key}`);
    }
  });
  
  console.log(`✅ Cleared ${clearedCount} encryption-related localStorage keys`);
  
  // Clear sessionStorage as well
  const sessionKeys = Object.keys(sessionStorage);
  let sessionClearedCount = 0;
  
  sessionKeys.forEach(key => {
    if (key.includes('whisperwave_private_key_') || 
        key.includes('encryption_') || 
        key.includes('crypto_')) {
      sessionStorage.removeItem(key);
      sessionClearedCount++;
      console.log(`🗑️ Cleared from session: ${key}`);
    }
  });
  
  console.log(`✅ Cleared ${sessionClearedCount} encryption-related sessionStorage keys`);
  
  console.log('🎉 All encryption data cleared! Please refresh the page.');
  
  // Suggest page refresh
  if (confirm('Encryption data cleared. Would you like to refresh the page now?')) {
    window.location.reload();
  }
})();

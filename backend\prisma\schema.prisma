// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  password    String   // Hashed password
  avatarUrl   String?
  publicKey   String?  // User's public key for encryption
  status      UserStatus @default(OFFLINE)
  statusMessage String?
  lastSeen    DateTime @default(now())
  isOnline    Boolean  @default(false)
  
  // Relationships
  channels    Channel[] @relation("ChannelMembers")
  messages    Message[]
  ownedChannels Channel[] @relation("ChannelOwner")
  
  // Blocking system
  blockedUsers     User[] @relation("UserBlocks")
  blockedByUsers   User[] @relation("UserBlocks")
  
  // Invitations
  sentInvitations  Invitation[] @relation("IncomingInvitations")
  receivedInvitations Invitation[] @relation("OutgoingInvitations")
  
  // Friendships
  sentFriendRequests     Friendship[] @relation("FriendshipRequester")
  receivedFriendRequests Friendship[] @relation("FriendshipAddressee")
  
  // Direct Messages
  sentDirectMessages     DirectMessage[] @relation("DirectMessageSender")
  receivedDirectMessages DirectMessage[] @relation("DirectMessageReceiver")
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("users")
}

model Channel {
  id          String    @id @default(cuid())
  name        String
  description String?
  isPrivate   Boolean   @default(false)
  
  // Channel owner
  owner       User      @relation("ChannelOwner", fields: [ownerId], references: [id])
  ownerId     String
  
  // Channel members
  members     User[]    @relation("ChannelMembers")
  messages    Message[]
  invitations Invitation[]
  
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("channels")
}

model Message {
  id          String    @id @default(cuid())
  content     String    // Encrypted content
  messageType MessageType @default(TEXT)
  
  // File attachments
  fileUrl     String?
  fileName    String?
  fileSize    Int?
  fileMimeType String?
  
  // Relationships
  channel     Channel   @relation(fields: [channelId], references: [id], onDelete: Cascade)
  channelId   String
  author      User      @relation(fields: [authorId], references: [id])
  authorId    String
  
  // Message metadata
  isEdited    Boolean   @default(false)
  editedAt    DateTime?
  
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("messages")
}

model Invitation {
  id        String   @id @default(cuid())
  channel   Channel  @relation(fields: [channelId], references: [id], onDelete: Cascade)
  channelId String
  invitedBy User     @relation("IncomingInvitations", fields: [invitedById], references: [id])
  invitedById String
  invitee    User     @relation("OutgoingInvitations", fields: [inviteeId], references: [id])
  inviteeId String
  status    InvitationStatus @default(PENDING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("invitations")
}

model Friendship {
  id             String   @id @default(cuid())
  requester      User     @relation("FriendshipRequester", fields: [requesterId], references: [id])
  requesterId    String
  addressee      User     @relation("FriendshipAddressee", fields: [addresseeId], references: [id])
  addresseeId    String
  status         FriendshipStatus @default(PENDING)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("friendships")
}

model DirectMessage {
  id             String   @id @default(cuid())
  content        String   // Encrypted content
  messageType    MessageType @default(TEXT)
  sender         User     @relation("DirectMessageSender", fields: [senderId], references: [id])
  senderId       String
  receiver       User     @relation("DirectMessageReceiver", fields: [receiverId], references: [id])
  receiverId     String
  isRead         Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("direct_messages")

}

// Enums

enum FriendshipStatus {
  PENDING
  ACCEPTED
  DECLINED
}

// Enums

enum InvitationStatus {
  PENDING
  ACCEPTED
  DECLINED
}

// Session Model
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("sessions")
}

// Enums
enum UserStatus {
  ONLINE
  AWAY
  BUSY
  OFFLINE
}

enum MessageType {
  TEXT
  FILE
  IMAGE
  SYSTEM
}

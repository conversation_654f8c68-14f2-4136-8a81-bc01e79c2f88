'use client';

import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchFriends } from '@/store/slices/friendsSlice';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { UserPlus, MessageCircle, Users, Clock } from 'lucide-react';
import { setCurrentConversationUser, fetchConversation } from '@/store/slices/directMessagesSlice';

interface Friend {
  id: string;
  username: string;
  email: string;
  avatarUrl?: string;
  status: 'ONLINE' | 'AWAY' | 'BUSY' | 'OFFLINE';
  isOnline: boolean;
}

interface FriendRequest {
  id: string;
  requester: Friend;
  addressee: Friend;
  status: 'PENDING' | 'ACCEPTED' | 'DECLINED';
  createdAt: string;
}

interface FriendsPanelProps {
  onStartDirectMessage?: (friendId: string) => void;
}

const FriendsPanel: React.FC<FriendsPanelProps> = ({ onStartDirectMessage }) => {
  const dispatch = useAppDispatch();
  const { friends, requests, isLoading, error } = useAppSelector(state => state.friends);
  const { user, token } = useAppSelector(state => state.auth);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Friend[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [friendRequests, setFriendRequests] = useState<{sent: FriendRequest[], received: FriendRequest[]}>({
    sent: [],
    received: []
  });

  useEffect(() => {
    if (token) {
      dispatch(fetchFriends());
      fetchFriendRequests();
    }
  }, [dispatch, token]);

  const fetchFriendRequests = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/friends/requests`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setFriendRequests(data);
      }
    } catch (error) {
      console.error('Error fetching friend requests:', error);
    }
  };

  const searchUsers = async () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/users/search?query=${encodeURIComponent(searchTerm)}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        // Backend returns { users: [...] }
        const users = data.users || data;
        // Ensure we always set an array to prevent map errors
        setSearchResults(Array.isArray(users) ? users : []);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const sendFriendRequest = async (addresseeId: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/friends/requests`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ addresseeId }),
      });

      if (response.ok) {
        // Refresh friend requests
        fetchFriendRequests();
      }
    } catch (error) {
      console.error('Error sending friend request:', error);
    }
  };

  const manageFriendRequest = async (friendshipId: string, action: 'accept' | 'decline') => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/friends/requests/manage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ friendshipId, action }),
      });

      if (response.ok) {
        // Refresh friends and requests
        dispatch(fetchFriends());
        fetchFriendRequests();
      }
    } catch (error) {
      console.error('Error managing friend request:', error);
    }
  };

  const getStatusColor = (status: string, isOnline: boolean) => {
    if (!isOnline) return 'bg-gray-400';
    switch (status) {
      case 'ONLINE': return 'bg-green-400';
      case 'AWAY': return 'bg-yellow-400';
      case 'BUSY': return 'bg-red-400';
      default: return 'bg-gray-400';
    }
  };

  const startDirectMessage = (friendId: string) => {
    if (!friendId) {
      console.error('Cannot start DM: friend ID is missing');
      return;
    }

    console.log('Starting DM with friend ID:', friendId);
    
    // Set the current conversation user in Redux
    dispatch(setCurrentConversationUser(friendId));
    
    // Fetch the conversation with this friend
    dispatch(fetchConversation({ otherUserId: friendId }));
    
    // Switch to direct messages view
    if (onStartDirectMessage) {
      onStartDirectMessage(friendId);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <Users className="w-5 h-5" />
          Friends
        </h2>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button className="w-full" variant="outline">
              <UserPlus className="w-4 h-4 mr-2" />
              Find Friends
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white">
            <DialogHeader>
              <DialogTitle>Find Friends</DialogTitle>
              <DialogDescription>
                Search for users by username or email to send friend requests.
              </DialogDescription>
            </DialogHeader>
            
            <div className="flex gap-2">
              <Input
                placeholder="Search by username or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && searchUsers()}
              />
              <Button onClick={searchUsers} disabled={isSearching}>
                Search
              </Button>
            </div>

            <div className="max-h-60 overflow-y-auto space-y-2">
              {isSearching ? (
                <div className="text-center py-4 text-gray-500">Searching...</div>
              ) : searchResults.length === 0 ? (
                searchTerm ? (
                  <div className="text-center py-4 text-gray-500">No users found</div>
                ) : (
                  <div className="text-center py-4 text-gray-500">Search for users to add as friends</div>
                )
              ) : (
                searchResults.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-2 border rounded bg-white">
                    <div className="flex items-center gap-2">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={user.avatarUrl} alt={user.username} />
                        <AvatarFallback>{user.username.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <span className="font-medium">{user.username}</span>
                    </div>
                    <Button 
                      size="sm" 
                      onClick={() => sendFriendRequest(user.id)}
                      disabled={false}
                    >
                      Add Friend
                    </Button>
                  </div>
                ))
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Friend Requests */}
        {(friendRequests.received?.length > 0 || friendRequests.sent?.length > 0) && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Friend Requests
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {friendRequests.received?.filter(request => request?.requester).map((request) => (
                <div key={request.id} className="bg-blue-50 rounded-lg border border-blue-200 p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={request.requester?.avatarUrl} alt={request.requester?.username || 'User'} />
                      <AvatarFallback>{request.requester?.username?.charAt(0)?.toUpperCase() || 'U'}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-semibold text-gray-900">{request.requester?.username || 'Unknown User'}</div>
                      <div className="text-sm text-blue-600">wants to be friends</div>
                    </div>
                  </div>
                  <div className="flex gap-2 justify-end">
                    <Button 
                      size="sm" 
                      onClick={() => manageFriendRequest(request.id, 'accept')}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-1.5 text-sm font-medium"
                    >
                      Accept
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => manageFriendRequest(request.id, 'decline')}
                      className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 px-4 py-1.5 text-sm font-medium"
                    >
                      Decline
                    </Button>
                  </div>
                </div>
              ))}
              
              {friendRequests.sent?.filter(request => request?.addressee).map((request) => (
                <div key={request.id} className="bg-gray-50 rounded-lg border border-gray-200 p-4">
                  <div className="flex items-center gap-3 mb-2">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={request.addressee?.avatarUrl} alt={request.addressee?.username || 'User'} />
                      <AvatarFallback>{request.addressee?.username?.charAt(0)?.toUpperCase() || 'U'}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-semibold text-gray-900">{request.addressee?.username || 'Unknown User'}</div>
                      <div className="text-sm text-gray-600">friend request sent</div>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-300 px-3 py-1">Pending</Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Friends List */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">
              All Friends ({Array.isArray(friends) ? friends.length : 0})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-4">Loading friends...</div>
            ) : error ? (
              <div className="text-center py-4 text-red-500">
                Error loading friends: {error}
              </div>
            ) : !Array.isArray(friends) ? (
              <div className="text-center py-4 text-yellow-500">
                Invalid friends data. Please refresh.
              </div>
            ) : friends.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No friends yet. Send some friend requests!
              </div>
            ) : (
              <div className="space-y-2">
                {friends.filter(friend => friend?.id).map((friend) => (
                  <div key={friend.friendshipId || friend.id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={friend?.avatarUrl} alt={friend?.username || 'User'} />
                          <AvatarFallback>{friend?.username?.charAt(0)?.toUpperCase() || 'U'}</AvatarFallback>
                        </Avatar>
                        <div 
                          className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(friend?.status || 'OFFLINE', friend?.isOnline || false)}`}
                        />
                      </div>
                      <div>
                        <div className="font-medium">{friend?.username || 'Unknown User'}</div>
                        <div className="text-xs text-gray-500">
                          {friend?.isOnline ? friend?.status : 'Offline'}
                        </div>
                      </div>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => startDirectMessage(friend?.id)}
                      disabled={!friend?.id}
                      title="Send Direct Message"
                    >
                      <MessageCircle className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FriendsPanel;

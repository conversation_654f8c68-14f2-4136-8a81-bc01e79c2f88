import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Channel {
  id: string;
  name: string;
  description?: string;
  isPrivate: boolean;
  ownerId: string;
  owner: {
    id: string;
    username: string;
    email: string;
  };
  members: {
    id: string;
    username: string;
    email: string;
    status: string;
    isOnline: boolean;
  }[];
  _count?: {
    messages: number;
    members: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface ChannelState {
  userChannels: Channel[];
  publicChannels: Channel[];
  isLoading: boolean;
  error: string | null;
  selectedChannel: Channel | null;
}

const initialState: ChannelState = {
  userChannels: [],
  publicChannels: [],
  isLoading: false,
  error: null,
  selectedChannel: null,
};

// Async thunks for API calls
export const fetchUserChannels = createAsyncThunk(
  'channels/fetchUserChannels',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/channels/my-channels`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user channels');
      }

      const data = await response.json();
      return data.channels;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchPublicChannels = createAsyncThunk(
  'channels/fetchPublicChannels',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/channels/public`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch public channels');
      }

      const data = await response.json();
      return data.channels;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const createChannel = createAsyncThunk(
  'channels/createChannel',
  async (channelData: { name: string; description?: string; isPrivate?: boolean }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/channels`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(channelData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create channel');
      }

      const data = await response.json();
      return data.channel;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const joinChannel = createAsyncThunk(
  'channels/joinChannel',
  async (channelId: string, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/channels/${channelId}/join`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to join channel');
      }

      return channelId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const leaveChannel = createAsyncThunk(
  'channels/leaveChannel',
  async (channelId: string, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/channels/${channelId}/leave`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to leave channel');
      }

      return channelId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateChannel = createAsyncThunk(
  'channels/updateChannel',
  async ({ channelId, data }: { channelId: string; data: { name?: string; description?: string; isPrivate?: boolean } }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/channels/${channelId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update channel');
      }

      const result = await response.json();
      return result.channel;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const deleteChannel = createAsyncThunk(
  'channels/deleteChannel',
  async (channelId: string, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/channels/${channelId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete channel');
      }

      return channelId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const channelSlice = createSlice({
  name: 'channels',
  initialState,
  reducers: {
    setSelectedChannel: (state, action: PayloadAction<Channel | null>) => {
      state.selectedChannel = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearChannels: (state) => {
      state.userChannels = [];
      state.publicChannels = [];
      state.selectedChannel = null;
    },
    // Real-time channel updates
    addNewPublicChannel: (state, action: PayloadAction<Channel>) => {
      // Add new public channel to the list if it doesn't exist
      const exists = state.publicChannels.some(channel => channel.id === action.payload.id);
      if (!exists) {
        state.publicChannels.unshift(action.payload);
      }
    },
    updateChannelData: (state, action: PayloadAction<Channel>) => {
      const { id } = action.payload;
      
      // Update in user channels
      const userChannelIndex = state.userChannels.findIndex(channel => channel.id === id);
      if (userChannelIndex !== -1) {
        state.userChannels[userChannelIndex] = { ...state.userChannels[userChannelIndex], ...action.payload };
      }
      
      // Update in public channels
      const publicChannelIndex = state.publicChannels.findIndex(channel => channel.id === id);
      if (publicChannelIndex !== -1) {
        state.publicChannels[publicChannelIndex] = { ...state.publicChannels[publicChannelIndex], ...action.payload };
      }
      
      // Update selected channel if it matches
      if (state.selectedChannel?.id === id) {
        state.selectedChannel = { ...state.selectedChannel, ...action.payload };
      }
    },
    updateChannelMemberCount: (state, action: PayloadAction<{ channelId: string; memberCount: number }>) => {
      const { channelId, memberCount } = action.payload;
      
      // Update in user channels
      const userChannel = state.userChannels.find(channel => channel.id === channelId);
      if (userChannel && userChannel._count) {
        userChannel._count.members = memberCount;
      }
      
      // Update in public channels
      const publicChannel = state.publicChannels.find(channel => channel.id === channelId);
      if (publicChannel && publicChannel._count) {
        publicChannel._count.members = memberCount;
      }
      
      // Update selected channel
      if (state.selectedChannel?.id === channelId && state.selectedChannel._count) {
        state.selectedChannel._count.members = memberCount;
      }
    },
    removeDeletedChannel: (state, action: PayloadAction<string>) => {
      const channelId = action.payload;
      
      // Remove from user channels
      state.userChannels = state.userChannels.filter(channel => channel.id !== channelId);
      
      // Remove from public channels
      state.publicChannels = state.publicChannels.filter(channel => channel.id !== channelId);
      
      // Clear selected channel if it was deleted
      if (state.selectedChannel?.id === channelId) {
        state.selectedChannel = null;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch user channels
      .addCase(fetchUserChannels.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserChannels.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userChannels = action.payload;
      })
      .addCase(fetchUserChannels.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch public channels
      .addCase(fetchPublicChannels.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPublicChannels.fulfilled, (state, action) => {
        state.isLoading = false;
        state.publicChannels = action.payload;
      })
      .addCase(fetchPublicChannels.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Create channel
      .addCase(createChannel.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createChannel.fulfilled, (state, action) => {
        state.isLoading = false;
        state.userChannels.unshift(action.payload);
      })
      .addCase(createChannel.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Join channel
      .addCase(joinChannel.fulfilled, (state, action) => {
        // Refresh channels after joining
        state.error = null;
      })
      .addCase(joinChannel.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // Leave channel
      .addCase(leaveChannel.fulfilled, (state, action) => {
        state.userChannels = state.userChannels.filter(channel => channel.id !== action.payload);
        if (state.selectedChannel?.id === action.payload) {
          state.selectedChannel = null;
        }
      })
      .addCase(leaveChannel.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // Update channel
      .addCase(updateChannel.fulfilled, (state, action) => {
        const index = state.userChannels.findIndex(channel => channel.id === action.payload.id);
        if (index !== -1) {
          state.userChannels[index] = action.payload;
        }
        if (state.selectedChannel?.id === action.payload.id) {
          state.selectedChannel = action.payload;
        }
      })
      .addCase(updateChannel.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // Delete channel
      .addCase(deleteChannel.fulfilled, (state, action) => {
        state.userChannels = state.userChannels.filter(channel => channel.id !== action.payload);
        if (state.selectedChannel?.id === action.payload) {
          state.selectedChannel = null;
        }
      })
      .addCase(deleteChannel.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { 
  setSelectedChannel, 
  clearError, 
  clearChannels,
  addNewPublicChannel,
  updateChannelData,
  updateChannelMemberCount,
  removeDeletedChannel
} = channelSlice.actions;
export default channelSlice.reducer;

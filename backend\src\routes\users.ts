import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { 
  searchUsers, 
  getUserProfile, 
  getAllUsers,
  updatePublicKey,
  getUserPublicKey
} from '../controllers/userController';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// User discovery routes
router.get('/search', searchUsers);
router.get('/all', getAllUsers);
router.get('/:userId', getUserProfile);

// Public key management
router.put('/public-key', updatePublicKey);
router.get('/:userId/public-key', getUserPublicKey);

export default router;

import prisma from '../src/utils/database';

async function fullCleanup() {
  console.log('🚀 Starting full database cleanup...');
  
  try {
    // 1. Delete all messages
    const channelMessagesResult = await prisma.message.deleteMany({});
    console.log(`✅ Deleted ${channelMessagesResult.count} channel messages`);

    // 2. Delete all direct messages
    const directMessagesResult = await prisma.directMessage.deleteMany({});
    console.log(`✅ Deleted ${directMessagesResult.count} direct messages`);

    // 3. Set all users offline
    const usersResult = await prisma.user.updateMany({
      where: {
        isOnline: true
      },
      data: {
        isOnline: false,
        status: 'OFFLINE',
        lastSeen: new Date()
      }
    });
    console.log(`✅ Set ${usersResult.count} users to offline status`);

    // 4. Optional: Clear friend requests (uncomment if needed)
    // const friendRequestsResult = await prisma.friendship.deleteMany({
    //   where: {
    //     status: 'PENDING'
    //   }
    // });
    // console.log(`✅ Deleted ${friendRequestsResult.count} pending friend requests`);

    console.log('🎉 Full cleanup completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Channel messages: ${channelMessagesResult.count} deleted`);
    console.log(`   - Direct messages: ${directMessagesResult.count} deleted`);
    console.log(`   - Users set offline: ${usersResult.count}`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
  
  process.exit(0);
}

fullCleanup();

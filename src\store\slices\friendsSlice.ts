import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';

interface Friend {
  id: string;
  username: string;
  avatarUrl?: string;
}

interface Friendship {
  friendshipId: string;
  friend: Friend;
  status: 'PENDING' | 'ACCEPTED' | 'DECLINED';
}

interface FriendsState {
  friends: Friendship[];
  requests: Friendship[];
  isLoading: boolean;
  error: string | null;
}

const initialState: FriendsState = {
  friends: [],
  requests: [],
  isLoading: false,
  error: null,
};

export const fetchFriends = createAsyncThunk('friends/fetch', async (_, { getState }) => {
  const state = getState() as RootState;
  const token = state.auth.token;

  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/friends`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch friends');
  }

  const data = await response.json();
  return data;
});

const friendsSlice = createSlice({
  name: 'friends',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchFriends.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFriends.fulfilled, (state, action: PayloadAction<Friendship[]>) => {
        state.isLoading = false;
        state.friends = action.payload;
      })
      .addCase(fetchFriends.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch friends';
      });
  },
});

export default friendsSlice.reducer;

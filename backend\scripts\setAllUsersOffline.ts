import prisma from '../src/utils/database';

async function setAllUsersOffline() {
  console.log('🔄 Setting all users to offline status...');
  
  try {
    const result = await prisma.user.updateMany({
      where: {
        isOnline: true
      },
      data: {
        isOnline: false,
        status: 'OFFLINE',
        lastSeen: new Date()
      }
    });

    console.log(`✅ Set ${result.count} users to offline status`);
  } catch (error) {
    console.error('❌ Error setting users offline:', error);
  } finally {
    await prisma.$disconnect();
  }
  
  process.exit(0);
}

setAllUsersOffline();

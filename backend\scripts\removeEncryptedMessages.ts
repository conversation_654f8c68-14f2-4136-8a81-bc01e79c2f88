import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function removeEncryptedMessages() {
  try {
    console.log('🔍 Searching for encrypted messages...');
    
    // Find all messages that contain encrypted JSON structure
    const allMessages = await prisma.directMessage.findMany({
      select: {
        id: true,
        content: true,
        createdAt: true,
        sender: {
          select: {
            username: true
          }
        },
        receiver: {
          select: {
            username: true
          }
        }
      }
    });

    console.log(`📊 Total messages found: ${allMessages.length}`);
    
    const encryptedMessages = [];
    
    for (const message of allMessages) {
      try {
        const parsed = JSON.parse(message.content);
        if (parsed.encrypted) {
          encryptedMessages.push(message);
          console.log(`🔒 Found encrypted message: ${message.id} from ${message.sender.username} to ${message.receiver.username}`);
        }
      } catch {
        // Not JSON, skip
      }
    }
    
    console.log(`🎯 Found ${encryptedMessages.length} encrypted messages to remove`);
    
    if (encryptedMessages.length === 0) {
      console.log('✅ No encrypted messages found! Database is clean.');
      return;
    }
    
    // Delete all encrypted messages
    const messageIds = encryptedMessages.map(msg => msg.id);
    
    const deleteResult = await prisma.directMessage.deleteMany({
      where: {
        id: {
          in: messageIds
        }
      }
    });
    
    console.log(`🗑️ Successfully deleted ${deleteResult.count} encrypted messages`);
    
    // Verify cleanup
    const remainingMessages = await prisma.directMessage.findMany({
      select: {
        id: true,
        content: true
      }
    });
    
    let remainingEncrypted = 0;
    for (const message of remainingMessages) {
      try {
        const parsed = JSON.parse(message.content);
        if (parsed.encrypted) {
          remainingEncrypted++;
        }
      } catch {
        // Not JSON, skip
      }
    }
    
    console.log(`📈 Verification: ${remainingMessages.length} total messages remain, ${remainingEncrypted} encrypted messages remain`);
    
    if (remainingEncrypted === 0) {
      console.log('🎉 SUCCESS: All encrypted messages have been removed!');
      console.log('💡 The app should now work without decryption errors');
      console.log('📝 New messages will be sent as plain text until encryption is fully fixed');
    } else {
      console.log(`⚠️ WARNING: ${remainingEncrypted} encrypted messages still remain`);
    }
    
  } catch (error) {
    console.error('❌ Error removing encrypted messages:', error);
    throw error;
  }
}

removeEncryptedMessages()
  .then(() => {
    console.log('✅ Encrypted message cleanup completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  });

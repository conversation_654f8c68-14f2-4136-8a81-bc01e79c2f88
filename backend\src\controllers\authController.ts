import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import prisma from '../utils/database';
import { config } from '../config/env';
import { AuthenticatedRequest } from '../middleware/auth';

// User registration
export const register = async (req: Request, res: Response) => {
  try {
    const { email, username, password } = req.body;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({ where: { email } });

    if (existingUser) {
      return res.status(400).json({ error: 'Email already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
      },
    });

    return res.status(201).json({ message: 'User registered successfully', user });
  } catch (error) {
    console.error('Error in register:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

// User login
export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const user = await prisma.user.findUnique({ where: { email } });

    if (!user) {
      return res.status(400).json({ error: 'Invalid email or password' });
    }

    // Compare passwords
    const isValidPassword = await bcrypt.compare(password, user.password);

    if (!isValidPassword) {
      return res.status(400).json({ error: 'Invalid email or password' });
    }

    // Create JWT token  
    const payload = { userId: user.id };
    const options = { expiresIn: config.JWT_EXPIRES_IN };
    const token = jwt.sign(payload, config.JWT_SECRET, options);

    // Update user online status when logging in
    await prisma.user.update({
      where: { id: user.id },
      data: {
        isOnline: true,
        status: 'ONLINE',
        lastSeen: new Date()
      }
    });

    return res.status(200).json({ message: 'Login successful', token, user });
  } catch (error) {
    console.error('Error in login:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

// User logout
export const logout = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    if (userId) {
      // Update user status to offline
      await prisma.user.update({
        where: { id: userId },
        data: {
          isOnline: false,
          status: 'OFFLINE',
          lastSeen: new Date()
        }
      });
      
      console.log(`User ${req.user?.username} logged out`);
    }

    return res.status(200).json({ message: 'Logout successful' });
  } catch (error) {
    console.error('Error in logout:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

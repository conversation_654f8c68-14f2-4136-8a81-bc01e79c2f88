import prisma from './database';

/**
 * Clean up users who have been inactive for more than 5 minutes
 * This helps handle cases where socket disconnect events weren't fired properly
 */
export async function cleanupStaleUserSessions(): Promise<void> {
  try {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    const result = await prisma.user.updateMany({
      where: {
        isOnline: true,
        lastSeen: {
          lt: fiveMinutesAgo
        }
      },
      data: {
        isOnline: false,
        status: 'OFFLINE',
        lastSeen: new Date()
      }
    });

    if (result.count > 0) {
      console.log(`Cleaned up ${result.count} stale user sessions`);
    }
  } catch (error) {
    console.error('Error cleaning up stale user sessions:', error);
  }
}

/**
 * Start periodic cleanup of stale user sessions
 * Runs every 2 minutes
 */
export function startUserStatusCleanup(): NodeJS.Timeout {
  console.log('Starting periodic user status cleanup...');
  
  // Run cleanup immediately
  cleanupStaleUserSessions();
  
  // Then run every 2 minutes
  const interval = setInterval(cleanupStaleUserSessions, 2 * 60 * 1000);
  
  return interval;
}

/**
 * Stop periodic cleanup
 */
export function stopUserStatusCleanup(interval: NodeJS.Timeout): void {
  clearInterval(interval);
  console.log('Stopped user status cleanup');
}

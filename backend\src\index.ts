import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));

// Rate limiting - more lenient in development
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'), // 1 minute in dev
  max: parseInt(process.env.RATE_LIMIT_MAX || (process.env.NODE_ENV === 'development' ? '1000' : '100')), // 1000 in dev, 100 in prod
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false
});
app.use(limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));


// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    message: 'WhisperWave server is running',
    timestamp: new Date().toISOString()
  });
});

// Setup Socket.IO message handling
import { setupMessageHandling } from './socket/messageHandler';
import { setSocketInstance } from './utils/socket';
import { startUserStatusCleanup } from './utils/userStatusCleanup';

// Initialize socket instance for use in controllers
setSocketInstance(io);
setupMessageHandling(io);

// Start periodic cleanup of stale user sessions
const cleanupInterval = startUserStatusCleanup();

// Import routes
import authRoutes from './routes/auth';
import messageRoutes from './routes/messages';
import channelRoutes from './routes/channels';
import userRoutes from './routes/users';
import invitationRoutes from './routes/invitations';
import friendsRoutes from './routes/friends';
import directMessagesRoutes from './routes/directMessages';

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/channels', channelRoutes);
app.use('/api/users', userRoutes);
app.use('/api/invitations', invitationRoutes);
app.use('/api/friends', friendsRoutes);
app.use('/api/direct-messages', directMessagesRoutes);

// Default API route
app.use('/api', (req, res) => {
  res.status(200).json({ message: 'WhisperWave API is running' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`🌊 WhisperWave server is running on port ${PORT}`);
  console.log(`📡 Socket.IO server is ready for connections`);
  console.log(`🚀 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM signal. Starting graceful shutdown...');
  
  // Stop the cleanup interval
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    console.log('✅ Stopped user status cleanup');
  }
  
  // Close the server
  server.close(() => {
    console.log('✅ HTTP server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT signal. Starting graceful shutdown...');
  
  // Stop the cleanup interval
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    console.log('✅ Stopped user status cleanup');
  }
  
  // Close the server
  server.close(() => {
    console.log('✅ HTTP server closed');
    process.exit(0);
  });
});

export { app, server, io };

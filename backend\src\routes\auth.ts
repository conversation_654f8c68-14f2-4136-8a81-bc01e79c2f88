import { Router } from 'express';
import { register, login, logout } from '../controllers/authController';
import { validateRequest, registerSchema, loginSchema } from '../middleware/validation';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';

const router = Router();

// Register route
router.post('/register', validateRequest(registerSchema), register);

// Login route
router.post('/login', validateRequest(loginSchema), login);

// Logout route (protected)
router.post('/logout', authenticateToken, logout);

// Get current user (protected route)
router.get('/me', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    res.status(200).json({
      message: 'User authenticated',
      user: req.user
    });
  } catch (error) {
    console.error('Error getting current user:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;

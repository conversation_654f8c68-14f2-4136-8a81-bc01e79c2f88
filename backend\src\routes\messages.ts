import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getMessages,
  sendMessage,
  deleteMessage
} from '../controllers/messageController';

const router = Router();

// All message routes require authentication
router.use(authenticateToken);

// GET /api/messages/:channelId - Get messages for a channel
router.get('/:channelId', getMessages);

// POST /api/messages - Send a message (REST API fallback)
router.post('/', sendMessage);

// DELETE /api/messages/:messageId - Delete a message
router.delete('/:messageId', deleteMessage);

export default router;

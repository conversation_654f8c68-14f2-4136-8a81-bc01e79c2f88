import { 
  generateKey<PERSON><PERSON>, 
  exportPub<PERSON><PERSON><PERSON>, 
  importPub<PERSON><PERSON><PERSON>, 
  deriveSharedKey, 
  encryptMessage, 
  decryptMessage,
  arrayBufferToBase64,
  base64ToArrayBuffer
} from '@/lib/crypto';

interface ChannelKeys {
  channelId: string;
  keyPair: CryptoKeyPair;
  publicKeyBase64: string;
  sharedKeys: Map<string, CryptoKey>; // userId -> shared key
}

interface DirectMessageKeys {
  otherUserId: string;
  sharedKey: CryptoKey;
  myPublicKey: string;
  theirPublicKey: string;
}

class EncryptionService {
  private channelKeys: Map<string, ChannelKeys> = new Map();
  private dmKeys: Map<string, DirectMessageKeys> = new Map(); // otherUserId -> DM keys
  private userKeyPair: CryptoKeyPair | null = null;
  private userPublicKeyBase64: string | null = null;
  private isInitialized: boolean = false;
  private currentUserId: string | null = null;

  // Set current user ID for key storage
  setCurrentUser(userId: string): void {
    this.currentUserId = userId;
  }

  // Load private key from localStorage
  private async loadStoredPrivateKey(): Promise<CryptoKey | null> {
    if (!this.currentUserId) return null;
    
    try {
      const keyStorageKey = `whisperwave_private_key_${this.currentUserId}`;
      const storedKeyData = localStorage.getItem(keyStorageKey);
      
      if (!storedKeyData) {
        console.log('📝 No stored private key found');
        return null;
      }
      
      console.log('📝 Loading stored private key...');
      const keyData = JSON.parse(storedKeyData);
      
      // Import the stored private key
      const privateKey = await window.crypto.subtle.importKey(
        'jwk',
        keyData.privateKey,
        {
          name: 'ECDH',
          namedCurve: 'P-256'
        },
        true,
        ['deriveKey']
      );
      
      // Import the corresponding public key
      const publicKey = await window.crypto.subtle.importKey(
        'jwk',
        keyData.publicKey,
        {
          name: 'ECDH',
          namedCurve: 'P-256'
        },
        true,
        []
      );
      
      this.userKeyPair = { privateKey, publicKey };
      this.userPublicKeyBase64 = keyData.publicKeyBase64;
      
      console.log('✅ Successfully loaded stored key pair');
      return privateKey;
    } catch (error) {
      console.error('❌ Failed to load stored private key:', error);
      // Clear corrupted data
      if (this.currentUserId) {
        const keyStorageKey = `whisperwave_private_key_${this.currentUserId}`;
        localStorage.removeItem(keyStorageKey);
      }
      return null;
    }
  }

  // Save private key to localStorage
  private async savePrivateKey(): Promise<void> {
    if (!this.currentUserId || !this.userKeyPair) return;
    
    try {
      console.log('💾 Saving private key to localStorage...');
      
      // Export both keys to JWK format
      const privateKeyJwk = await window.crypto.subtle.exportKey('jwk', this.userKeyPair.privateKey);
      const publicKeyJwk = await window.crypto.subtle.exportKey('jwk', this.userKeyPair.publicKey);
      
      const keyData = {
        privateKey: privateKeyJwk,
        publicKey: publicKeyJwk,
        publicKeyBase64: this.userPublicKeyBase64,
        createdAt: new Date().toISOString()
      };
      
      const keyStorageKey = `whisperwave_private_key_${this.currentUserId}`;
      localStorage.setItem(keyStorageKey, JSON.stringify(keyData));
      
      console.log('✅ Private key saved successfully');
    } catch (error) {
      console.error('❌ Failed to save private key:', error);
    }
  }

  // Initialize user's master key pair with persistence
  async initializeUserKeys(userId?: string): Promise<{ publicKey: string }> {
    if (userId) {
      this.setCurrentUser(userId);
    }
    
    if (!this.userKeyPair) {
      // Try to load existing key pair first
      const storedPrivateKey = await this.loadStoredPrivateKey();
      
      if (!storedPrivateKey) {
        // Generate new key pair if none exists
        console.log('🔑 Generating new key pair for user...');
        this.userKeyPair = await generateKeyPair();
        const publicKeyBuffer = await exportPublicKey(this.userKeyPair.publicKey);
        this.userPublicKeyBase64 = arrayBufferToBase64(publicKeyBuffer);
        
        // Save the new key pair
        await this.savePrivateKey();
        
        console.log('✅ User key pair generated and stored', {
          hasPrivateKey: !!this.userKeyPair.privateKey,
          hasPublicKey: !!this.userKeyPair.publicKey,
          publicKeyLength: this.userPublicKeyBase64?.length
        });
      }
      
      this.isInitialized = true;
    } else {
      console.log('🔄 Using existing user key pair');
    }
    
    return { publicKey: this.userPublicKeyBase64! };
  }

  // Generate or get keys for a channel
  async getChannelKeys(channelId: string): Promise<ChannelKeys> {
    if (!this.channelKeys.has(channelId)) {
      const keyPair = await generateKeyPair();
      const publicKeyBuffer = await exportPublicKey(keyPair.publicKey);
      const publicKeyBase64 = arrayBufferToBase64(publicKeyBuffer);
      
      const channelKeys: ChannelKeys = {
        channelId,
        keyPair,
        publicKeyBase64,
        sharedKeys: new Map()
      };
      
      this.channelKeys.set(channelId, channelKeys);
    }
    
    return this.channelKeys.get(channelId)!;
  }

  // Add another user's public key and derive shared secret
  async addUserToChannel(channelId: string, userId: string, userPublicKeyBase64: string): Promise<void> {
    const channelKeys = await this.getChannelKeys(channelId);
    
    try {
      const userPublicKeyBuffer = base64ToArrayBuffer(userPublicKeyBase64);
      const userPublicKey = await importPublicKey(userPublicKeyBuffer);
      const sharedKey = await deriveSharedKey(channelKeys.keyPair.privateKey, userPublicKey);
      
      channelKeys.sharedKeys.set(userId, sharedKey);
    } catch (error) {
      console.error(`Failed to add user ${userId} to channel ${channelId}:`, error);
      throw error;
    }
  }

  // Encrypt a message for a specific channel
  async encryptMessageForChannel(
    channelId: string, 
    message: string, 
    recipientUserId?: string
  ): Promise<{ encryptedContent: string; iv: string; senderPublicKey: string } | null> {
    try {
      const channelKeys = await this.getChannelKeys(channelId);
      
      // Ensure a shared key is available
      if (!recipientUserId || !channelKeys.sharedKeys.has(recipientUserId)) {
        console.error(`No shared key for recipient ${recipientUserId}`);
        return null;
      }

      const sharedKey = channelKeys.sharedKeys.get(recipientUserId)!;
      const { ciphertext, iv } = await encryptMessage(message, sharedKey);
      
      return {
        encryptedContent: arrayBufferToBase64(ciphertext),
        iv: arrayBufferToBase64(iv.buffer),
        senderPublicKey: channelKeys.publicKeyBase64
      };
    } catch (error) {
      console.error('Failed to encrypt message:', error);
      return null;
    }
  }

  // Decrypt a message from a channel
  async decryptMessageFromChannel(
    channelId: string, 
    encryptedContent: string, 
    iv: string, 
    senderPublicKey: string
  ): Promise<string | null> {
    try {
      const channelKeys = await this.getChannelKeys(channelId);
      
      // For now, use channel's own key pair to decrypt (simplified approach)
      const ciphertextBuffer = base64ToArrayBuffer(encryptedContent);
      const ivBuffer = new Uint8Array(base64ToArrayBuffer(iv));
      
      const decryptedMessage = await decryptMessage(
        ciphertextBuffer, 
        ivBuffer, 
        channelKeys.keyPair.privateKey as any
      );
      
      return decryptedMessage;
    } catch (error) {
      console.error('Failed to decrypt message:', error);
      return null;
    }
  }

  // Get user's public key for sharing
  getUserPublicKey(): string | null {
    return this.userPublicKeyBase64;
  }

  // Get channel's public key for sharing
  async getChannelPublicKey(channelId: string): Promise<string> {
    const channelKeys = await this.getChannelKeys(channelId);
    return channelKeys.publicKeyBase64;
  }

  // Setup encryption for direct messages with another user
  async setupDirectMessageEncryption(otherUserId: string, theirPublicKeyBase64: string): Promise<void> {
    console.log(`🔧 Starting DM encryption setup for user ${otherUserId}`, {
      hasUserKeyPair: !!this.userKeyPair,
      hasUserPublicKey: !!this.userPublicKeyBase64,
      theirPublicKeyLength: theirPublicKeyBase64?.length
    });
    
    if (!this.userKeyPair || !this.userPublicKeyBase64) {
      console.log('🔄 User keys not available, initializing...');
      await this.initializeUserKeys();
    }

    try {
      console.log('🔑 Deriving shared key...');
      console.log('User key pair available:', {
        hasPrivateKey: !!this.userKeyPair?.privateKey,
        hasPublicKey: !!this.userKeyPair?.publicKey,
        privateKeyType: this.userKeyPair?.privateKey?.type
      });
      
      const theirPublicKeyBuffer = base64ToArrayBuffer(theirPublicKeyBase64);
      const theirPublicKey = await importPublicKey(theirPublicKeyBuffer);
      const sharedKey = await deriveSharedKey(this.userKeyPair!.privateKey, theirPublicKey);

      console.log('✅ Shared key derived successfully', {
        sharedKeyType: sharedKey.type,
        sharedKeyUsages: sharedKey.usages
      });

      const dmKeys: DirectMessageKeys = {
        otherUserId,
        sharedKey,
        myPublicKey: this.userPublicKeyBase64!,
        theirPublicKey: theirPublicKeyBase64
      };

      this.dmKeys.set(otherUserId, dmKeys);
      console.log(`✅ DM encryption setup complete for user ${otherUserId}`);
    } catch (error) {
      console.error(`❌ Failed to setup DM encryption with user ${otherUserId}:`, error);
      console.error('Error details:', {
        hasUserKeyPair: !!this.userKeyPair,
        hasPrivateKey: !!this.userKeyPair?.privateKey,
        privateKeyType: this.userKeyPair?.privateKey?.type,
        privateKeyUsages: this.userKeyPair?.privateKey?.usages,
        errorName: error.name,
        errorMessage: error.message
      });
      throw error;
    }
  }

  // Encrypt a direct message
  async encryptDirectMessage(
    otherUserId: string,
    message: string,
    theirPublicKeyBase64?: string
  ): Promise<{ encryptedContent: string; iv: string; senderPublicKey: string } | null> {
    try {
      // Setup encryption if not already done
      if (!this.dmKeys.has(otherUserId) && theirPublicKeyBase64) {
        await this.setupDirectMessageEncryption(otherUserId, theirPublicKeyBase64);
      }

      const dmKeys = this.dmKeys.get(otherUserId);
      if (!dmKeys) {
        console.error(`No DM keys found for user ${otherUserId}`);
        return null;
      }

      const { ciphertext, iv } = await encryptMessage(message, dmKeys.sharedKey);

      return {
        encryptedContent: arrayBufferToBase64(ciphertext),
        iv: arrayBufferToBase64(iv.buffer),
        senderPublicKey: dmKeys.myPublicKey  // Store sender's public key for decryption
      };
    } catch (error) {
      console.error('Failed to encrypt direct message:', error);
      return null;
    }
  }

  // Decrypt a direct message
  async decryptDirectMessage(
    otherUserId: string,
    encryptedContent: string,
    iv: string,
    senderPublicKeyBase64?: string
  ): Promise<string | null> {
    console.log(`🔓 Attempting to decrypt DM from user ${otherUserId}`, {
      hasDmKeys: this.dmKeys.has(otherUserId),
      senderPublicKeyProvided: !!senderPublicKeyBase64,
      senderPublicKeyLength: senderPublicKeyBase64?.length,
      encryptedContentLength: encryptedContent.length,
      ivLength: iv.length
    });
    
    // Debug: Log key comparison for verification
    if (senderPublicKeyBase64 && this.userPublicKeyBase64) {
      const senderMatchesOurs = senderPublicKeyBase64 === this.userPublicKeyBase64;
      console.log('🔍 Key comparison check:', {
        senderPublicKey: senderPublicKeyBase64.substring(0, 30) + '...',
        ourPublicKey: this.userPublicKeyBase64.substring(0, 30) + '...',
        keysAreIdentical: senderMatchesOurs,
        status: senderMatchesOurs ? 'Keys match - may be old message format' : 'Keys different (expected for new messages)'
      });
    }
    
    try {
      // Ensure we have user keys initialized
      if (!this.userKeyPair || !this.userPublicKeyBase64) {
        console.log('🔄 Initializing user keys for decryption...');
        await this.initializeUserKeys();
      }

      // Check if we need to set up or update DM encryption
      const existingDmKeys = this.dmKeys.get(otherUserId);
      const needsSetup = !existingDmKeys || 
        (senderPublicKeyBase64 && existingDmKeys.theirPublicKey !== senderPublicKeyBase64);
        
      if (needsSetup && senderPublicKeyBase64) {
        console.log(`🔧 ${existingDmKeys ? 'Updating' : 'Setting up'} DM encryption for user ${otherUserId}`);
        console.log('Key comparison:', {
          existingKey: existingDmKeys?.theirPublicKey?.substring(0, 20) + '...',
          newKey: senderPublicKeyBase64?.substring(0, 20) + '...'
        });
        await this.setupDirectMessageEncryption(otherUserId, senderPublicKeyBase64);
        console.log(`✅ DM encryption ${existingDmKeys ? 'updated' : 'setup complete'} for user ${otherUserId}`);
      }

      const dmKeys = this.dmKeys.get(otherUserId);
      if (!dmKeys) {
        console.error(`❌ No DM keys found for user ${otherUserId}`);
        console.log('Available DM keys:', Array.from(this.dmKeys.keys()));
        return null;
      }

      console.log(`🔑 Using DM keys for user ${otherUserId}`, {
        hasSharedKey: !!dmKeys.sharedKey,
        myPublicKey: dmKeys.myPublicKey?.substring(0, 20) + '...',
        theirPublicKey: dmKeys.theirPublicKey?.substring(0, 20) + '...',
        providedSenderKey: senderPublicKeyBase64?.substring(0, 20) + '...'
      });

      const ciphertextBuffer = base64ToArrayBuffer(encryptedContent);
      const ivBuffer = new Uint8Array(base64ToArrayBuffer(iv));

      const decryptedMessage = await decryptMessage(
        ciphertextBuffer,
        ivBuffer,
        dmKeys.sharedKey
      );

      console.log(`✅ Successfully decrypted message from user ${otherUserId}`);
      return decryptedMessage;
    } catch (error) {
      console.error('❌ Failed to decrypt direct message:', error);
      
      // If we have a sender public key, try recreating the shared key as a fallback
      if (senderPublicKeyBase64) {
        console.log('🔄 Attempting fallback decryption with fresh key derivation...');
        console.log('🔍 Debug info for fallback:', {
          currentUserPublicKey: this.userPublicKeyBase64?.substring(0, 20) + '...',
          senderPublicKey: senderPublicKeyBase64?.substring(0, 20) + '...'
        });
        
        try {
          const theirPublicKeyBuffer = base64ToArrayBuffer(senderPublicKeyBase64);
          const theirPublicKey = await importPublicKey(theirPublicKeyBuffer);
          const freshSharedKey = await deriveSharedKey(this.userKeyPair!.privateKey, theirPublicKey);
          
          console.log('✅ Fresh shared key derived, attempting decryption...');
          const ciphertextBuffer = base64ToArrayBuffer(encryptedContent);
          const ivBuffer = new Uint8Array(base64ToArrayBuffer(iv));
          
          const decryptedMessage = await decryptMessage(
            ciphertextBuffer,
            ivBuffer,
            freshSharedKey
          );
          
          console.log('✅ Fallback decryption successful! Updating stored keys...');
          // Update our stored keys with the working combination
          const dmKeys: DirectMessageKeys = {
            otherUserId,
            sharedKey: freshSharedKey,
            myPublicKey: this.userPublicKeyBase64!,
            theirPublicKey: senderPublicKeyBase64
          };
          this.dmKeys.set(otherUserId, dmKeys);
          
          return decryptedMessage;
        } catch (fallbackError) {
          console.error('❌ Fallback decryption also failed:', fallbackError);
          console.error('Fallback error details:', {
            errorName: fallbackError.name,
            errorMessage: fallbackError.message,
            hasValidPrivateKey: !!this.userKeyPair?.privateKey,
            privateKeyUsages: this.userKeyPair?.privateKey?.usages
          });
          
          // Last resort: try to understand what's in the encrypted content
          console.log('🔍 Encrypted content analysis:', {
            encryptedContentBase64: encryptedContent,
            ivBase64: iv,
            decodedCiphertextLength: base64ToArrayBuffer(encryptedContent).byteLength,
            decodedIvLength: base64ToArrayBuffer(iv).byteLength
          });
        }
      }
      
      return null;
    }
  }

  // Check if we have DM encryption setup for a user
  hasDirectMessageEncryption(otherUserId: string): boolean {
    return this.dmKeys.has(otherUserId);
  }

  // Debug method to inspect current key state
  debugKeyState(): void {
    console.log('🔍 Encryption Service Key State Debug:', {
      isInitialized: this.isInitialized,
      hasUserKeyPair: !!this.userKeyPair,
      hasUserPublicKey: !!this.userPublicKeyBase64,
      userPublicKeyBase64: this.userPublicKeyBase64?.substring(0, 20) + '...',
      dmKeysCount: this.dmKeys.size,
      dmKeyUserIds: Array.from(this.dmKeys.keys()),
      channelKeysCount: this.channelKeys.size
    });
    
    // Debug each DM key setup
    for (const [userId, dmKeys] of this.dmKeys.entries()) {
      console.log(`🔑 DM Keys for user ${userId}:`, {
        hasSharedKey: !!dmKeys.sharedKey,
        myPublicKey: dmKeys.myPublicKey?.substring(0, 20) + '...',
        theirPublicKey: dmKeys.theirPublicKey?.substring(0, 20) + '...'
      });
    }
  }

  // Clear all keys (logout)
  clearAllKeys(): void {
    this.channelKeys.clear();
    this.dmKeys.clear();
    this.userKeyPair = null;
    this.userPublicKeyBase64 = null;
    this.isInitialized = false;
  }

  // Debug method to clear localStorage keys (for testing)
  clearStoredKeys(): void {
    if (this.currentUserId) {
      const keyStorageKey = `whisperwave_private_key_${this.currentUserId}`;
      localStorage.removeItem(keyStorageKey);
      console.log('🗑️ Cleared stored keys for user:', this.currentUserId);
    }
    this.clearAllKeys();
  }

  // Reset everything for a fresh session
  resetForNewSession(newUserId?: string): void {
    console.log('🔄 Resetting encryption service for new session:', newUserId);
    
    // Clear all current state
    this.clearAllKeys();
    
    // Update current user ID
    if (newUserId) {
      this.currentUserId = newUserId;
    }
    
    // Clear any stored keys for all users to prevent conflicts
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.includes('whisperwave_private_key_')) {
        localStorage.removeItem(key);
        console.log('🗑️ Cleared stored key:', key);
      }
    });
    
    console.log('✅ Encryption service reset complete');
  }
}

// Singleton instance
const encryptionService = new EncryptionService();
export default encryptionService;

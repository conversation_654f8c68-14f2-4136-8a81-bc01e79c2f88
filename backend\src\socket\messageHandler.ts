import { Server as SocketIOServer, Socket } from 'socket.io';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';
import { sendMessageSchema } from '../controllers/messageController';
import { config } from '../config/env';

const prisma = new PrismaClient();

interface AuthenticatedSocket extends Socket {
  userId?: string;
  username?: string;
}

interface TypingData {
  channelId: string;
  username: string;
}

interface JoinChannelData {
  channelId: string;
}

interface ChannelUpdateData {
  channelId: string;
  name?: string;
  description?: string;
  isPrivate?: boolean;
}

interface ChannelCreatedData {
  channel: {
    id: string;
    name: string;
    description?: string;
    isPrivate: boolean;
    createdAt: Date;
    updatedAt: Date;
    memberCount?: number;
  };
}

interface ChannelMembershipData {
  channelId: string;
  userId: string;
  username: string;
  action: 'joined' | 'left';
}

interface DirectMessageData {
  receiverId: string;
  content: string;
  messageType?: 'TEXT' | 'FILE' | 'IMAGE' | 'SYSTEM';
}

interface FriendRequestData {
  addresseeId: string;
}

interface FriendRequestActionData {
  friendshipId: string;
  action: 'accept' | 'decline';
}

// JWT verification middleware for socket connections
export const authenticateSocket = async (socket: AuthenticatedSocket, next: any) => {
  try {
    const token = socket.handshake.auth.token;
    
    if (!token) {
      return next(new Error('Authentication error: No token provided'));
    }

    const decoded = jwt.verify(token, config.JWT_SECRET) as any;
    
    // Verify user exists in database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, username: true, status: true }
    });

    if (!user) {
      return next(new Error('Authentication error: User not found'));
    }

    // Attach user info to socket
    socket.userId = user.id;
    socket.username = user.username;

    // Update user online status
    await prisma.user.update({
      where: { id: user.id },
      data: { 
        isOnline: true,
        status: 'ONLINE',
        lastSeen: new Date()
      }
    });

    next();
  } catch (error) {
    console.error('Socket authentication error:', error);
    next(new Error('Authentication error: Invalid token'));
  }
};

// Setup message handling for Socket.IO
export const setupMessageHandling = (io: SocketIOServer) => {
  
  // Use authentication middleware
  io.use(authenticateSocket);

  io.on('connection', (socket: AuthenticatedSocket) => {
    console.log(`User connected: ${socket.username} (${socket.id})`);

    // Join user to their personal room for direct messages
    if (socket.userId) {
      socket.join(`user:${socket.userId}`);
      console.log(`${socket.username} joined personal room: user:${socket.userId}`);
    }

    // Handle joining a channel
    socket.on('joinChannel', async (data: JoinChannelData) => {
      try {
        const { channelId } = data;
        
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Verify user has access to the channel
        const channel = await prisma.channel.findFirst({
          where: {
            id: channelId,
            members: {
              some: { id: socket.userId }
            }
          },
          include: {
            members: {
              select: {
                id: true,
                username: true,
                status: true,
                isOnline: true
              }
            }
          }
        });

        if (!channel) {
          socket.emit('error', { message: 'Access denied to this channel' });
          return;
        }

        // Join the socket room for this channel
        socket.join(channelId);
        
        // Notify other channel members that user joined
        socket.to(channelId).emit('userJoined', {
          userId: socket.userId,
          username: socket.username,
          channelId
        });

        // Send channel info back to the user
        socket.emit('channelJoined', {
          channelId,
          channelName: channel.name,
          members: channel.members
        });

        console.log(`${socket.username} joined channel: ${channel.name}`);

      } catch (error) {
        console.error('Error joining channel:', error);
        socket.emit('error', { message: 'Failed to join channel' });
      }
    });

    // Handle leaving a channel
    socket.on('leaveChannel', async (data: JoinChannelData) => {
      try {
        const { channelId } = data;
        
        // Leave the socket room
        socket.leave(channelId);
        
        // Notify other channel members
        socket.to(channelId).emit('userLeft', {
          userId: socket.userId,
          username: socket.username,
          channelId
        });

        console.log(`${socket.username} left channel: ${channelId}`);

      } catch (error) {
        console.error('Error leaving channel:', error);
        socket.emit('error', { message: 'Failed to leave channel' });
      }
    });

    // Handle sending messages
    socket.on('sendMessage', async (messageData: any) => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Validate message data
        const validatedData = sendMessageSchema.parse(messageData);

        // Verify user has access to the channel
        const channel = await prisma.channel.findFirst({
          where: {
            id: validatedData.channelId,
            members: {
              some: { id: socket.userId }
            }
          }
        });

        if (!channel) {
          socket.emit('error', { message: 'Access denied to this channel' });
          return;
        }

        // Create the message in the database
        const message = await prisma.message.create({
          data: {
            ...validatedData,
            authorId: socket.userId
          },
          include: {
            author: {
              select: {
                id: true,
                username: true,
                avatarUrl: true,
                status: true
              }
            }
          }
        });

        // Broadcast the message to all channel members
        io.to(validatedData.channelId).emit('messageReceived', {
          message,
          channelId: validatedData.channelId
        });

        console.log(`Message sent by ${socket.username} in channel ${validatedData.channelId}`);

      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { 
          message: 'Failed to send message',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Handle typing indicators
    socket.on('typing', (data: TypingData) => {
      socket.to(data.channelId).emit('userTyping', {
        userId: socket.userId,
        username: socket.username,
        channelId: data.channelId,
        isTyping: true
      });
    });

    socket.on('stopTyping', (data: TypingData) => {
      socket.to(data.channelId).emit('userTyping', {
        userId: socket.userId,
        username: socket.username,
        channelId: data.channelId,
        isTyping: false
      });
    });

    // Handle channel creation broadcast
    socket.on('channelCreated', async (data: ChannelCreatedData) => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Broadcast new public channel to all users
        if (!data.channel.isPrivate) {
          socket.broadcast.emit('newPublicChannel', {
            channel: data.channel,
            createdBy: {
              userId: socket.userId,
              username: socket.username
            }
          });
        }

        console.log(`Channel created: ${data.channel.name} by ${socket.username}`);

      } catch (error) {
        console.error('Error broadcasting channel creation:', error);
      }
    });

    // Handle channel updates broadcast
    socket.on('channelUpdated', async (data: ChannelUpdateData) => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Get updated channel info to broadcast
        const channel = await prisma.channel.findUnique({
          where: { id: data.channelId },
          include: {
            members: {
              select: { id: true }
            },
            _count: {
              select: { members: true }
            }
          }
        });

        if (channel) {
          // Broadcast to all channel members
          io.to(data.channelId).emit('channelUpdated', {
            channel: {
              id: channel.id,
              name: channel.name,
              description: channel.description,
              isPrivate: channel.isPrivate,
              memberCount: channel._count.members,
              updatedAt: channel.updatedAt
            },
            updatedBy: {
              userId: socket.userId,
              username: socket.username
            }
          });

          // If channel became public, broadcast to all users
          if (!channel.isPrivate) {
            socket.broadcast.emit('channelVisibilityChanged', {
              channel: {
                id: channel.id,
                name: channel.name,
                description: channel.description,
                isPrivate: channel.isPrivate,
                memberCount: channel._count.members
              }
            });
          }

          console.log(`Channel updated: ${channel.name} by ${socket.username}`);
        }

      } catch (error) {
        console.error('Error broadcasting channel update:', error);
      }
    });

    // Handle channel membership changes
    socket.on('channelMembershipChanged', async (data: ChannelMembershipData) => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Get updated channel info
        const channel = await prisma.channel.findUnique({
          where: { id: data.channelId },
          include: {
            _count: {
              select: { members: true }
            }
          }
        });

        if (channel) {
          // Broadcast membership change to channel members
          io.to(data.channelId).emit('channelMembershipChanged', {
            channelId: data.channelId,
            channelName: channel.name,
            userId: data.userId,
            username: data.username,
            action: data.action,
            memberCount: channel._count.members
          });

          // If it's a public channel, update the public channel list for all users
          if (!channel.isPrivate) {
            socket.broadcast.emit('publicChannelMemberCountChanged', {
              channelId: data.channelId,
              memberCount: channel._count.members
            });
          }

          console.log(`User ${data.username} ${data.action} channel: ${channel.name}`);
        }

      } catch (error) {
        console.error('Error broadcasting membership change:', error);
      }
    });

    // Handle channel deletion
    socket.on('channelDeleted', async (data: { channelId: string; channelName: string }) => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Notify all users about channel deletion
        io.emit('channelDeleted', {
          channelId: data.channelId,
          channelName: data.channelName,
          deletedBy: {
            userId: socket.userId,
            username: socket.username
          }
        });

        console.log(`Channel deleted: ${data.channelName} by ${socket.username}`);

      } catch (error) {
        console.error('Error broadcasting channel deletion:', error);
      }
    });

// Handle sending direct messages
    socket.on('sendDirectMessage', async (data: DirectMessageData) => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Verify receiver exists
        const receiver = await prisma.user.findUnique({
          where: { id: data.receiverId }
        });

        if (!receiver) {
          socket.emit('error', { message: 'Receiver not found' });
          return;
        }

        // Create direct message
        const directMessage = await prisma.directMessage.create({
          data: {
            senderId: socket.userId,
            receiverId: data.receiverId,
            content: data.content,
            messageType: data.messageType || 'TEXT'
          },
          include: {
            sender: {
              select: {
                id: true,
                username: true,
                avatarUrl: true
              }
            },
            receiver: {
              select: {
                id: true,
                username: true,
                avatarUrl: true
              }
            }
          }
        });

        // Emit the message to the receiver's personal room
        io.to(`user:${data.receiverId}`).emit('directMessageReceived', directMessage);
        // Emit the message back to sender for confirmation
        socket.emit('directMessageSent', directMessage);

        console.log(`Direct message sent from ${socket.username} to ${receiver.username}`);

      } catch (error) {
        console.error('Error sending direct message:', error);
        socket.emit('error', { message: 'Failed to send direct message' });
      }
    });

    // Handle friend request sending
    socket.on('sendFriendRequest', async (data: FriendRequestData) => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Check if addressee exists
        const addressee = await prisma.user.findUnique({
          where: { id: data.addresseeId }
        });

        if (!addressee) {
          socket.emit('error', { message: 'Addressee not found' });
          return;
        }

        // Create friend request
        const friendRequest = await prisma.friendship.create({
          data: {
            requesterId: socket.userId,
            addresseeId: data.addresseeId,
            status: 'PENDING'
          },
          include: {
            requester: {
              select: {
                id: true,
                username: true,
                avatarUrl: true
              }
            },
            addressee: {
              select: {
                id: true,
                username: true,
                avatarUrl: true
              }
            }
          }
        });

        // Emit the friend request to the addressee's personal room
        io.to(`user:${data.addresseeId}`).emit('friendRequestReceived', friendRequest);
        console.log(`Friend request sent from ${socket.username} to ${addressee.username}`);

      } catch (error) {
        console.error('Error sending friend request:', error);
        socket.emit('error', { message: 'Failed to send friend request' });
      }
    });

    // Handle friend request action (accept/decline)
    socket.on('manageFriendRequest', async (data: FriendRequestActionData) => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        // Find the friend request
        const friendship = await prisma.friendship.findUnique({
          where: { id: data.friendshipId },
          include: {
            requester: {
              select: {
                id: true,
                username: true,
                avatarUrl: true
              }
            },
            addressee: {
              select: {
                id: true,
                username: true,
                avatarUrl: true
              }
            }
          }
        });

        if (!friendship) {
          socket.emit('error', { message: 'Friend request not found' });
          return;
        }

        // Update friendship status
        const updatedFriendship = await prisma.friendship.update({
          where: { id: data.friendshipId },
          data: { status: data.action === 'accept' ? 'ACCEPTED' : 'DECLINED' },
          include: {
            requester: {
              select: {
                id: true,
                username: true,
                avatarUrl: true
              }
            },
            addressee: {
              select: {
                id: true,
                username: true,
                avatarUrl: true
              }
            }
          }
        });

        // Emit the friend request update event to the requester's personal room
        io.to(`user:${updatedFriendship.requesterId}`).emit('friendRequestUpdated', updatedFriendship);
        console.log(`Friend request ${data.action} by ${socket.username}`);

      } catch (error) {
        console.error('Error managing friend request:', error);
        socket.emit('error', { message: 'Failed to manage friend request' });
      }
    });

    // Handle heartbeat/ping to keep connection alive
    socket.on('heartbeat', () => {
      socket.emit('heartbeat-response', { timestamp: new Date().toISOString() });
    });

    // Handle explicit logout from client
    socket.on('logout', async () => {
      try {
        if (socket.userId) {
          // Update user offline status
          await prisma.user.update({
            where: { id: socket.userId },
            data: { 
              isOnline: false,
              status: 'OFFLINE',
              lastSeen: new Date()
            }
          });

          // Notify all rooms that user went offline
          socket.broadcast.emit('userOffline', {
            userId: socket.userId,
            username: socket.username
          });

          console.log(`${socket.username} logged out via socket (${socket.id})`);
        }
      } catch (error) {
        console.error('Error handling logout:', error);
      }
    });

    // Handle user disconnection
    socket.on('disconnect', async (reason) => {
      try {
        if (socket.userId) {
          // Check if there are other active sockets for this user
          const userSockets = await io.in(`user:${socket.userId}`).fetchSockets();
          const remainingConnections = userSockets.filter(s => s.id !== socket.id).length;

          // Only set offline if no other connections exist
          if (remainingConnections === 0) {
            await prisma.user.update({
              where: { id: socket.userId },
              data: { 
                isOnline: false,
                status: 'OFFLINE',
                lastSeen: new Date()
              }
            });

            // Notify all rooms that user went offline
            socket.broadcast.emit('userOffline', {
              userId: socket.userId,
              username: socket.username
            });

            console.log(`${socket.username} disconnected - no remaining connections (${socket.id}, reason: ${reason})`);
          } else {
            console.log(`${socket.username} disconnected but has ${remainingConnections} remaining connections (${socket.id}, reason: ${reason})`);
          }
        }
      } catch (error) {
        console.error('Error handling disconnect:', error);
      }
    });
  });
};

import { Request, Response } from 'express';
import { z } from 'zod';
import prisma from '../utils/database';

// Validation schemas
const sendDirectMessageSchema = z.object({
  receiverId: z.string().cuid(),
  content: z.string().min(1, 'Message content is required'),
  messageType: z.enum(['TEXT', 'FILE', 'IMAGE', 'SYSTEM']).optional().default('TEXT')
});

const getDirectMessagesSchema = z.object({
  otherUserId: z.string().cuid(),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0)
});

const markAsReadSchema = z.object({
  otherUserId: z.string().cuid()
});

// Send direct message
export const sendDirectMessage = async (req: Request, res: Response) => {
  try {
    const { receiverId, content, messageType } = sendDirectMessageSchema.parse(req.body);
    const senderId = req.user!.id;

    // Check if trying to send message to self
    if (senderId === receiverId) {
      return res.status(400).json({ error: 'Cannot send message to yourself' });
    }

    // Check if receiver exists
    const receiver = await prisma.user.findUnique({
      where: { id: receiverId }
    });

    if (!receiver) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if users are blocked
    const isBlocked = await prisma.user.findFirst({
      where: {
        OR: [
          { id: senderId, blockedUsers: { some: { id: receiverId } } },
          { id: receiverId, blockedUsers: { some: { id: senderId } } }
        ]
      }
    });

    if (isBlocked) {
      return res.status(400).json({ error: 'Cannot send message to this user' });
    }

    // Check if users are friends (optional: you might want to allow DMs only between friends)
    const areFriends = await prisma.friendship.findFirst({
      where: {
        OR: [
          { requesterId: senderId, addresseeId: receiverId, status: 'ACCEPTED' },
          { requesterId: receiverId, addresseeId: senderId, status: 'ACCEPTED' }
        ]
      }
    });

    // For now, allow DMs between all users, but you can uncomment this to restrict to friends only
    // if (!areFriends) {
    //   return res.status(400).json({ error: 'Can only send direct messages to friends' });
    // }

    // Create direct message
    const directMessage = await prisma.directMessage.create({
      data: {
        senderId,
        receiverId,
        content,
        messageType
      },
      include: {
        sender: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        },
        receiver: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        }
      }
    });

    res.status(201).json(directMessage);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    console.error('Error sending direct message:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get direct messages between current user and another user
export const getDirectMessages = async (req: Request, res: Response) => {
  try {
    const { otherUserId, limit, offset } = getDirectMessagesSchema.parse(req.query);
    const userId = req.user!.id;

    // Check if other user exists
    const otherUser = await prisma.user.findUnique({
      where: { id: otherUserId }
    });

    if (!otherUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if users are blocked
    const isBlocked = await prisma.user.findFirst({
      where: {
        OR: [
          { id: userId, blockedUsers: { some: { id: otherUserId } } },
          { id: otherUserId, blockedUsers: { some: { id: userId } } }
        ]
      }
    });

    if (isBlocked) {
      return res.status(400).json({ error: 'Cannot view messages with this user' });
    }

    // Get direct messages between the two users
    const messages = await prisma.directMessage.findMany({
      where: {
        OR: [
          { senderId: userId, receiverId: otherUserId },
          { senderId: otherUserId, receiverId: userId }
        ]
      },
      include: {
        sender: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        },
        receiver: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset
    });

    // Reverse to get chronological order (oldest first)
    const chronologicalMessages = messages.reverse();

    res.json(chronologicalMessages);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    console.error('Error fetching direct messages:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get direct message conversations (list of users with recent messages)
export const getDirectMessageConversations = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Get all direct messages involving the current user
    const messages = await prisma.directMessage.findMany({
      where: {
        OR: [
          { senderId: userId },
          { receiverId: userId }
        ]
      },
      include: {
        sender: {
          select: { id: true, username: true, email: true, avatarUrl: true, status: true, isOnline: true }
        },
        receiver: {
          select: { id: true, username: true, email: true, avatarUrl: true, status: true, isOnline: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Group by conversation partner and get the latest message for each
    const conversationsMap = new Map();

    for (const message of messages) {
      const otherUser = message.senderId === userId ? message.receiver : message.sender;
      const conversationKey = otherUser.id;

      if (!conversationsMap.has(conversationKey)) {
        // Count unread messages from this user to current user
        const unreadCount = await prisma.directMessage.count({
          where: {
            senderId: otherUser.id,
            receiverId: userId,
            isRead: false
          }
        });

        conversationsMap.set(conversationKey, {
          user: otherUser,
          lastMessage: message,
          unreadCount
        });
      }
    }

    // Convert map to array and sort by last message time
    const conversations = Array.from(conversationsMap.values()).sort(
      (a, b) => new Date(b.lastMessage.createdAt).getTime() - new Date(a.lastMessage.createdAt).getTime()
    );

    res.json(conversations);
  } catch (error) {
    console.error('Error fetching direct message conversations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Mark messages as read
export const markDirectMessagesAsRead = async (req: Request, res: Response) => {
  try {
    const { otherUserId } = markAsReadSchema.parse(req.body);
    const userId = req.user!.id;

    // Mark all unread messages from the other user as read
    const result = await prisma.directMessage.updateMany({
      where: {
        senderId: otherUserId,
        receiverId: userId,
        isRead: false
      },
      data: {
        isRead: true
      }
    });

    res.json({ 
      message: 'Messages marked as read',
      updatedCount: result.count
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    console.error('Error marking messages as read:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get unread direct message count
export const getUnreadDirectMessageCount = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const unreadCount = await prisma.directMessage.count({
      where: {
        receiverId: userId,
        isRead: false
      }
    });

    res.json({ unreadCount });
  } catch (error) {
    console.error('Error fetching unread message count:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

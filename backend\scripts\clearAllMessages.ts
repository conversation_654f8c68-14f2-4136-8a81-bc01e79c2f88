import prisma from '../src/utils/database';

async function clearAllMessages() {
  console.log('🧹 Clearing all chat messages and direct messages...');
  
  try {
    // Delete all channel messages
    const channelMessagesResult = await prisma.message.deleteMany({});
    console.log(`✅ Deleted ${channelMessagesResult.count} channel messages`);

    // Delete all direct messages
    const directMessagesResult = await prisma.directMessage.deleteMany({});
    console.log(`✅ Deleted ${directMessagesResult.count} direct messages`);

    console.log('🎉 All messages cleared successfully!');
  } catch (error) {
    console.error('❌ Error clearing messages:', error);
  } finally {
    await prisma.$disconnect();
  }
  
  process.exit(0);
}

clearAllMessages();

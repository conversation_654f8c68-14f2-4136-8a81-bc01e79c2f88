import { Response } from 'express';
import prisma from '../utils/database';
import { AuthenticatedRequest } from '../middleware/auth';

// Search/discover users
export const searchUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { query, limit = 10, offset = 0 } = req.query;
    const currentUserId = req.user.id;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const searchQuery = query.trim();
    if (searchQuery.length < 2) {
      return res.status(400).json({ error: 'Search query must be at least 2 characters' });
    }

    // Search users by username or email (excluding current user)
    const users = await prisma.user.findMany({
      where: {
        AND: [
          {
            NOT: {
              id: currentUserId
            }
          },
          {
            OR: [
              {
                username: {
                  contains: searchQuery,
                  mode: 'insensitive'
                }
              },
              {
                email: {
                  contains: searchQuery,
                  mode: 'insensitive'
                }
              }
            ]
          }
        ]
      },
      select: {
        id: true,
        username: true,
        email: true,
        avatarUrl: true,
        status: true,
        isOnline: true,
        lastSeen: true
      },
      take: Math.min(parseInt(limit as string) || 10, 50), // Max 50 results
      skip: parseInt(offset as string) || 0,
      orderBy: {
        username: 'asc'
      }
    });

    res.status(200).json({ users });
  } catch (error) {
    console.error('Error searching users:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get user profile by ID
export const getUserProfile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        email: true,
        avatarUrl: true,
        status: true,
        statusMessage: true,
        isOnline: true,
        lastSeen: true,
        createdAt: true,
        _count: {
          select: {
            channels: true,
            ownedChannels: true
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.status(200).json({ user });
  } catch (error) {
    console.error('Error getting user profile:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get all users (admin function - for development)
export const getAllUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { limit = 20, offset = 0 } = req.query;
    const currentUserId = req.user.id;

    const users = await prisma.user.findMany({
      where: {
        NOT: {
          id: currentUserId
        }
      },
      select: {
        id: true,
        username: true,
        email: true,
        avatarUrl: true,
        status: true,
        isOnline: true,
        lastSeen: true,
        createdAt: true
      },
      take: Math.min(parseInt(limit as string) || 20, 100),
      skip: parseInt(offset as string) || 0,
      orderBy: {
        createdAt: 'desc'
      }
    });

    const totalCount = await prisma.user.count({
      where: {
        NOT: {
          id: currentUserId
        }
      }
    });

    res.status(200).json({ 
      users, 
      pagination: {
        total: totalCount,
        limit: parseInt(limit as string) || 20,
        offset: parseInt(offset as string) || 0
      }
    });
  } catch (error) {
    console.error('Error getting all users:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Update user's public key for encryption
export const updatePublicKey = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { publicKey } = req.body;
    const userId = req.user.id;

    if (!publicKey || typeof publicKey !== 'string') {
      return res.status(400).json({ error: 'Public key is required' });
    }

    // Update user's public key
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { publicKey },
      select: {
        id: true,
        username: true,
        publicKey: true
      }
    });

    res.status(200).json({ 
      message: 'Public key updated successfully',
      user: updatedUser
    });
  } catch (error) {
    console.error('Error updating public key:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get user's public key by ID
export const getUserPublicKey = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        publicKey: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (!user.publicKey) {
      return res.status(404).json({ error: 'User has no public key set' });
    }

    res.status(200).json({ 
      userId: user.id,
      username: user.username,
      publicKey: user.publicKey
    });
  } catch (error) {
    console.error('Error getting user public key:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

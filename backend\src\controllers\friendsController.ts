import { Request, Response } from 'express';
import { z } from 'zod';
import prisma from '../utils/database';

// Validation schemas
const sendFriendRequestSchema = z.object({
  addresseeId: z.string().cuid()
});

const manageFriendRequestSchema = z.object({
  friendshipId: z.string().cuid(),
  action: z.enum(['accept', 'decline'])
});

const blockUserSchema = z.object({
  userId: z.string().cuid()
});

// Send friend request
export const sendFriendRequest = async (req: Request, res: Response) => {
  try {
    const { addresseeId } = sendFriendRequestSchema.parse(req.body);
    const requesterId = req.user!.id;

    // Check if trying to send request to self
    if (requesterId === addresseeId) {
      return res.status(400).json({ error: 'Cannot send friend request to yourself' });
    }

    // Check if user exists
    const addressee = await prisma.user.findUnique({
      where: { id: addresseeId }
    });

    if (!addressee) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if users are blocked
    const isBlocked = await prisma.user.findFirst({
      where: {
        OR: [
          { id: requesterId, blockedUsers: { some: { id: addresseeId } } },
          { id: addresseeId, blockedUsers: { some: { id: requesterId } } }
        ]
      }
    });

    if (isBlocked) {
      return res.status(400).json({ error: 'Cannot send friend request' });
    }

    // Check if friendship already exists
    const existingFriendship = await prisma.friendship.findFirst({
      where: {
        OR: [
          { requesterId, addresseeId },
          { requesterId: addresseeId, addresseeId: requesterId }
        ]
      }
    });

    if (existingFriendship) {
      if (existingFriendship.status === 'ACCEPTED') {
        return res.status(400).json({ error: 'Already friends' });
      }
      if (existingFriendship.status === 'PENDING') {
        return res.status(400).json({ error: 'Friend request already sent' });
      }
      // If declined, allow sending new request by updating existing one
      const updatedFriendship = await prisma.friendship.update({
        where: { id: existingFriendship.id },
        data: {
          requesterId,
          addresseeId,
          status: 'PENDING'
        },
        include: {
          requester: {
            select: { id: true, username: true, email: true, avatarUrl: true }
          },
          addressee: {
            select: { id: true, username: true, email: true, avatarUrl: true }
          }
        }
      });

      return res.status(200).json(updatedFriendship);
    }

    // Create new friend request
    const friendship = await prisma.friendship.create({
      data: {
        requesterId,
        addresseeId,
        status: 'PENDING'
      },
      include: {
        requester: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        },
        addressee: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        }
      }
    });

    res.status(201).json(friendship);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    console.error('Error sending friend request:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get friend requests (sent and received)
export const getFriendRequests = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const [sentRequests, receivedRequests] = await Promise.all([
      prisma.friendship.findMany({
        where: {
          requesterId: userId,
          status: 'PENDING'
        },
        include: {
          addressee: {
            select: { id: true, username: true, email: true, avatarUrl: true, status: true, lastSeen: true }
          }
        }
      }),
      prisma.friendship.findMany({
        where: {
          addresseeId: userId,
          status: 'PENDING'
        },
        include: {
          requester: {
            select: { id: true, username: true, email: true, avatarUrl: true, status: true, lastSeen: true }
          }
        }
      })
    ]);

    res.json({
      sent: sentRequests,
      received: receivedRequests
    });
  } catch (error) {
    console.error('Error fetching friend requests:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Accept or decline friend request
export const manageFriendRequest = async (req: Request, res: Response) => {
  try {
    const { friendshipId, action } = manageFriendRequestSchema.parse(req.body);
    const userId = req.user!.id;

    // Find the friendship request
    const friendship = await prisma.friendship.findUnique({
      where: { id: friendshipId },
      include: {
        requester: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        },
        addressee: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        }
      }
    });

    if (!friendship) {
      return res.status(404).json({ error: 'Friend request not found' });
    }

    // Only the addressee can accept/decline
    if (friendship.addresseeId !== userId) {
      return res.status(403).json({ error: 'Not authorized to manage this friend request' });
    }

    // Update friendship status
    const updatedFriendship = await prisma.friendship.update({
      where: { id: friendshipId },
      data: { status: action === 'accept' ? 'ACCEPTED' : 'DECLINED' },
      include: {
        requester: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        },
        addressee: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        }
      }
    });

    res.json(updatedFriendship);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    console.error('Error managing friend request:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get friends list
export const getFriends = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const friendships = await prisma.friendship.findMany({
      where: {
        OR: [
          { requesterId: userId, status: 'ACCEPTED' },
          { addresseeId: userId, status: 'ACCEPTED' }
        ]
      },
      include: {
        requester: {
          select: { id: true, username: true, email: true, avatarUrl: true, status: true, lastSeen: true, isOnline: true }
        },
        addressee: {
          select: { id: true, username: true, email: true, avatarUrl: true, status: true, lastSeen: true, isOnline: true }
        }
      }
    });

    // Transform data to return the friend (not self)
    const friends = friendships.map(friendship => {
      const friend = friendship.requesterId === userId ? friendship.addressee : friendship.requester;
      return {
        friendshipId: friendship.id,
        ...friend
      };
    });

    res.json(friends);
  } catch (error) {
    console.error('Error fetching friends:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Remove friend
export const removeFriend = async (req: Request, res: Response) => {
  try {
    const { friendshipId } = z.object({ friendshipId: z.string().cuid() }).parse(req.params);
    const userId = req.user!.id;

    // Find the friendship
    const friendship = await prisma.friendship.findUnique({
      where: { id: friendshipId }
    });

    if (!friendship) {
      return res.status(404).json({ error: 'Friendship not found' });
    }

    // Check if user is part of this friendship
    if (friendship.requesterId !== userId && friendship.addresseeId !== userId) {
      return res.status(403).json({ error: 'Not authorized to remove this friendship' });
    }

    // Delete the friendship
    await prisma.friendship.delete({
      where: { id: friendshipId }
    });

    res.json({ message: 'Friend removed successfully' });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    console.error('Error removing friend:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Block user
export const blockUser = async (req: Request, res: Response) => {
  try {
    const { userId: targetUserId } = blockUserSchema.parse(req.body);
    const userId = req.user!.id;

    if (userId === targetUserId) {
      return res.status(400).json({ error: 'Cannot block yourself' });
    }

    // Check if user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: targetUserId }
    });

    if (!targetUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Add to blocked users
    await prisma.user.update({
      where: { id: userId },
      data: {
        blockedUsers: {
          connect: { id: targetUserId }
        }
      }
    });

    // Remove any existing friendship
    await prisma.friendship.deleteMany({
      where: {
        OR: [
          { requesterId: userId, addresseeId: targetUserId },
          { requesterId: targetUserId, addresseeId: userId }
        ]
      }
    });

    res.json({ message: 'User blocked successfully' });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    console.error('Error blocking user:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Unblock user
export const unblockUser = async (req: Request, res: Response) => {
  try {
    const { userId: targetUserId } = blockUserSchema.parse(req.body);
    const userId = req.user!.id;

    // Remove from blocked users
    await prisma.user.update({
      where: { id: userId },
      data: {
        blockedUsers: {
          disconnect: { id: targetUserId }
        }
      }
    });

    res.json({ message: 'User unblocked successfully' });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    console.error('Error unblocking user:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get blocked users
export const getBlockedUsers = async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        blockedUsers: {
          select: { id: true, username: true, email: true, avatarUrl: true }
        }
      }
    });

    res.json(user?.blockedUsers || []);
  } catch (error) {
    console.error('Error fetching blocked users:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

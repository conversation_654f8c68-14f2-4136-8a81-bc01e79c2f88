"use client";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">🌊 WhisperWave</h1>
          <p className="text-gray-600">
            Secure, real-time chat with end-to-end encryption
          </p>
        </div>
        
        <div className="space-y-6">
          <div className="text-center space-y-2">
            <h3 className="text-lg font-semibold text-gray-800">Welcome to WhisperWave</h3>
            <p className="text-sm text-gray-600">
              A modern chat application built with Next.js, featuring secure messaging, 
              file sharing, and voice/video calls.
            </p>
          </div>
          
          <div className="flex flex-col space-y-3">
            <Link href="/auth/login">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                Sign In
              </button>
            </Link>
            
            <Link href="/auth/register">
              <button className="w-full border border-blue-600 text-blue-600 hover:bg-blue-50 font-medium py-3 px-4 rounded-lg transition-colors">
                Create Account
              </button>
            </Link>
          </div>
          
          <div className="text-center">
            <p className="text-xs text-gray-500">
              🔐 All messages are encrypted end-to-end for your privacy
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

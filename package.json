{"name": "whisperwave-temp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:frontend": "next dev", "dev:backend": "cd backend && npm run dev", "dev:all": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "build": "next build", "build:frontend": "next build", "build:backend": "cd backend && npm run build", "build:all": "npm run build:frontend && npm run build:backend", "start": "next start", "start:frontend": "next start", "start:backend": "cd backend && npm start", "lint": "next lint", "install:backend": "cd backend && npm install", "db:migrate": "cd backend && npm run db:migrate", "db:generate": "cd backend && npm run db:generate", "db:studio": "cd backend && npm run db:studio", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "jest", "test:backend": "cd backend && npm test"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.8.2", "@types/lodash": "^4.17.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-react": "^0.534.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-redux": "^9.2.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.4.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}
"use client";
import React from 'react';
import { Shield, ShieldAlert, ShieldCheck } from 'lucide-react';

interface EncryptionStatusProps {
  isEncrypted: boolean;
  hasError?: boolean;
  className?: string;
}

const EncryptionStatus: React.FC<EncryptionStatusProps> = ({ 
  isEncrypted, 
  hasError = false, 
  className = "" 
}) => {
  const getStatusIcon = () => {
    if (hasError) {
      return <ShieldAlert className="w-4 h-4 text-red-500" />;
    }
    if (isEncrypted) {
      return <ShieldCheck className="w-4 h-4 text-green-500" />;
    }
    return <Shield className="w-4 h-4 text-gray-400" />;
  };

  const getStatusText = () => {
    if (hasError) return "Encryption Error";
    if (isEncrypted) return "End-to-End Encrypted";
    return "Not Encrypted";
  };

  const getStatusColor = () => {
    if (hasError) return "text-red-500";
    if (isEncrypted) return "text-green-500";
    return "text-gray-400";
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {getStatusIcon()}
      <span className={`text-xs ${getStatusColor()}`}>
        {getStatusText()}
      </span>
    </div>
  );
};

export default EncryptionStatus;

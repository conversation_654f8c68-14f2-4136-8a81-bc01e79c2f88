import { useEffect } from 'react';
import { useAppDispatch } from '@/store/hooks';
import { 
  addNewPublicChannel, 
  updateChannelData, 
  updateChannelMemberCount, 
  removeDeletedChannel,
  fetchUserChannels 
} from '@/store/slices/channelSlice';
import socketService from '@/lib/socket';

export const useChannelRealtime = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (!socketService.isConnected()) {
      return;
    }

    // Listen for new public channels
    const handleNewPublicChannel = (data: any) => {
      console.log('New public channel created:', data);
      
      if (data.channel && !data.channel.isPrivate) {
        dispatch(addNewPublicChannel({
          id: data.channel.id,
          name: data.channel.name,
          description: data.channel.description,
          isPrivate: data.channel.isPrivate,
          ownerId: data.createdBy?.userId || '',
          owner: {
            id: data.createdBy?.userId || '',
            username: data.createdBy?.username || '',
            email: ''
          },
          members: [],
          _count: {
            messages: 0,
            members: data.channel.memberCount || 1
          },
          createdAt: data.channel.createdAt,
          updatedAt: data.channel.updatedAt
        }));
      }
    };

    // Listen for channel updates
    const handleChannelUpdate = (data: any) => {
      console.log('Channel updated:', data);
      
      if (data.channel) {
        dispatch(updateChannelData({
          id: data.channel.id,
          name: data.channel.name,
          description: data.channel.description,
          isPrivate: data.channel.isPrivate,
          ownerId: data.channel.ownerId || '',
          owner: data.channel.owner || { id: '', username: '', email: '' },
          members: data.channel.members || [],
          _count: {
            messages: data.channel.messageCount || 0,
            members: data.channel.memberCount || 0
          },
          createdAt: data.channel.createdAt,
          updatedAt: data.channel.updatedAt
        }));
      }
    };

    // Listen for membership changes
    const handleMembershipChange = (data: any) => {
      console.log('Channel membership changed:', data);
      
      if (data.channelId && typeof data.memberCount === 'number') {
        dispatch(updateChannelMemberCount({
          channelId: data.channelId,
          memberCount: data.memberCount
        }));
      }
      
      // If current user joined or left, refresh user channels
      if (data.action === 'joined') {
        dispatch(fetchUserChannels());
      }
    };

    // Listen for channel deletions
    const handleChannelDeletion = (data: any) => {
      console.log('Channel deleted:', data);
      
      if (data.channelId) {
        dispatch(removeDeletedChannel(data.channelId));
      }
    };

    // Set up event listeners
    socketService.onNewPublicChannel(handleNewPublicChannel);
    socketService.onChannelUpdate(handleChannelUpdate);
    socketService.onMembershipChange(handleMembershipChange);
    socketService.onChannelDeletion(handleChannelDeletion);

    // Cleanup function
    return () => {
      socketService.removeListener('newPublicChannel');
      socketService.removeListener('channelUpdated');
      socketService.removeListener('channelMembershipChanged');
      socketService.removeListener('channelDeleted');
    };
  }, [dispatch]);
};

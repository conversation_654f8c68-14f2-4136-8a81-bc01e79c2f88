import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { 
  sendInvitation,
  acceptInvitation,
  declineInvitation,
  getReceivedInvitations,
  getSentInvitations
} from '../controllers/invitationController';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// Invitation routes
router.post('/send/:channelId/:inviteeId', sendInvitation);
router.post('/accept/:invitationId', acceptInvitation);
router.post('/decline/:invitationId', declineInvitation);
router.get('/received', getReceivedInvitations);
router.get('/sent', getSentInvitations);

export default router;

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';
import encryptionService from '@/lib/encryptionService';

interface DirectMessage {
  id: string;
  content: string;
  senderId: string;
  receiverId: string;
  isRead: boolean;
  createdAt: string;
  sender: {
    id: string;
    username: string;
    avatarUrl?: string;
  };
  receiver: {
    id: string;
    username: string;
    avatarUrl?: string;
  };
}

interface Conversation {
  user: {
    id: string;
    username: string;
    avatarUrl?: string;
    status: 'ONLINE' | 'AWAY' | 'BUSY' | 'OFFLINE';
    isOnline: boolean;
  };
  lastMessage: DirectMessage;
  unreadCount: number;
}

interface DirectMessagesState {
  conversations: Conversation[];
  currentConversation: DirectMessage[];
  currentConversationUserId: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: DirectMessagesState = {
  conversations: [],
  currentConversation: [],
  currentConversationUserId: null,
  isLoading: false,
  error: null,
};

// Fetch all conversations
export const fetchConversations = createAsyncThunk(
  'directMessages/fetchConversations',
  async (_, { getState }) => {
    const state = getState() as RootState;
    const token = state.auth.token;

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/direct-messages/conversations`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch conversations');
    }

    return await response.json();
  }
);

// Fetch messages for a specific conversation
export const fetchConversation = createAsyncThunk(
  'directMessages/fetchConversation',
  async ({ otherUserId, limit = 50, offset = 0 }: { otherUserId: string; limit?: number; offset?: number }, { getState }) => {
    const state = getState() as RootState;
    const token = state.auth.token;

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/direct-messages?otherUserId=${otherUserId}&limit=${limit}&offset=${offset}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error('Failed to fetch conversation');
    }

    const messages = await response.json();
    return { messages, otherUserId };
  }
);

// Send a direct message with encryption
export const sendDirectMessage = createAsyncThunk(
  'directMessages/send',
  async ({ receiverId, content, recipientPublicKey }: { receiverId: string; content: string; recipientPublicKey?: string }, { getState }) => {
    const state = getState() as RootState;
    const token = state.auth.token;

    let finalContent = content;
    
    // Try to encrypt the message if we have the recipient's public key
    if (recipientPublicKey) {
      try {
        const encryptedData = await encryptionService.encryptDirectMessage(receiverId, content, recipientPublicKey);
        if (encryptedData) {
          finalContent = JSON.stringify({
            encrypted: true,
            content: encryptedData.encryptedContent,
            iv: encryptedData.iv,
            senderPublicKey: encryptedData.senderPublicKey
          });
        }
      } catch (error) {
        console.warn('Failed to encrypt direct message, sending as plain text:', error);
      }
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/direct-messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ receiverId, content: finalContent }),
    });

    if (!response.ok) {
      throw new Error('Failed to send message');
    }

    return await response.json();
  }
);

// Mark messages as read
export const markMessagesAsRead = createAsyncThunk(
  'directMessages/markAsRead',
  async (otherUserId: string, { getState }) => {
    const state = getState() as RootState;
    const token = state.auth.token;

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/direct-messages/mark-read`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ otherUserId }),
    });

    if (!response.ok) {
      throw new Error('Failed to mark messages as read');
    }

    return { otherUserId };
  }
);

const directMessagesSlice = createSlice({
  name: 'directMessages',
  initialState,
  reducers: {
    addDirectMessage: (state, action: PayloadAction<DirectMessage>) => {
      const message = action.payload;
      const currentConversationUserId = state.currentConversationUserId;
      
      // Add message to current conversation if it involves the currently active conversation
      // currentConversationUserId is the ID of the OTHER user we're chatting with
      if (currentConversationUserId && 
          (message.senderId === currentConversationUserId || message.receiverId === currentConversationUserId)) {
        // Check if message is not already in the conversation to avoid duplicates
        const messageExists = state.currentConversation.some(msg => msg.id === message.id);
        if (!messageExists) {
          state.currentConversation.push(message);
          // Sort messages by creation time to maintain order
          state.currentConversation.sort((a, b) => 
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        }
      }

      // Update conversations list - find conversation based on who the "other" user is
      let otherUserId: string;
      
      // Find the conversation that this message belongs to
      const conversationForSender = state.conversations.find(conv => conv.user.id === message.senderId);
      const conversationForReceiver = state.conversations.find(conv => conv.user.id === message.receiverId);
      
      if (conversationForSender) {
        otherUserId = message.senderId;
      } else if (conversationForReceiver) {
        otherUserId = message.receiverId;
      } else {
        // If no existing conversation, determine based on who's not the current conversation user
        otherUserId = message.senderId === currentConversationUserId ? message.receiverId : message.senderId;
      }

      const conversationIndex = state.conversations.findIndex(
        conv => conv.user.id === otherUserId
      );

      if (conversationIndex >= 0) {
        state.conversations[conversationIndex].lastMessage = message;
        // Only increment unread count if we're not currently viewing this conversation
        if (currentConversationUserId !== otherUserId) {
          state.conversations[conversationIndex].unreadCount += 1;
        }
      }
    },
    setCurrentConversationUser: (state, action: PayloadAction<string | null>) => {
      state.currentConversationUserId = action.payload;
      if (!action.payload) {
        state.currentConversation = [];
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch conversations
    builder
      .addCase(fetchConversations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action: PayloadAction<Conversation[]>) => {
        state.isLoading = false;
        state.conversations = action.payload;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch conversations';
      });

    // Fetch conversation
    builder
      .addCase(fetchConversation.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(
        fetchConversation.fulfilled,
        (state, action: PayloadAction<{ messages: DirectMessage[]; otherUserId: string }>) => {
          state.isLoading = false;
          state.currentConversation = action.payload.messages;
          state.currentConversationUserId = action.payload.otherUserId;
        }
      )
      .addCase(fetchConversation.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch conversation';
      });

    // Send direct message
    builder
      .addCase(sendDirectMessage.pending, (state) => {
        state.error = null;
      })
      .addCase(sendDirectMessage.fulfilled, (state, action: PayloadAction<DirectMessage>) => {
        // Message will be added via socket event or addDirectMessage action
      })
      .addCase(sendDirectMessage.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to send message';
      });

    // Mark as read
    builder
      .addCase(markMessagesAsRead.fulfilled, (state, action: PayloadAction<{ otherUserId: string }>) => {
        const conversationIndex = state.conversations.findIndex(
          conv => conv.user.id === action.payload.otherUserId
        );
        if (conversationIndex >= 0) {
          state.conversations[conversationIndex].unreadCount = 0;
        }
      });
  },
});

// Utility function to decrypt a message if it's encrypted
export const decryptMessageContent = async (message: DirectMessage, otherUserId: string): Promise<string> => {
  try {
    // Check if message is encrypted JSON
    let parsedContent;
    try {
      parsedContent = JSON.parse(message.content);
    } catch {
      // Not JSON, return as plain text
      return message.content;
    }

    if (parsedContent.encrypted) {
      try {
        // Try to decrypt with the improved error handling
        console.log('🔐 Attempting to decrypt message for user:', otherUserId);
        
        const decryptedContent = await encryptionService.decryptDirectMessage(
          otherUserId,
          parsedContent.content,
          parsedContent.iv,
          parsedContent.senderPublicKey
        );
        
        if (decryptedContent && decryptedContent.trim()) {
          console.log('✅ Successfully decrypted message');
          return decryptedContent;
        } else {
          console.log('⚠️ Decryption returned empty, using fallback');
          return '🔒 [Encrypted message - unable to decrypt]';
        }
      } catch (decryptError: any) {
        console.warn('🔐 Decryption failed safely, using fallback:', {
          errorName: decryptError?.name,
          messageId: message.id
        });
        return '🔒 [Encrypted message - decryption failed]';
      }
    }
    
    return message.content;
  } catch (error: any) {
    console.warn('⚠️ Message content error, returning safe fallback:', error?.message);
    return '📝 [Message content unavailable]';
  }
};

export const { addDirectMessage, setCurrentConversationUser, clearError } = directMessagesSlice.actions;
export default directMessagesSlice.reducer;

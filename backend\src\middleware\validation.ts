import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

export const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  username: z.string().min(3, 'Username must be at least 3 characters').max(20, 'Username must be less than 20 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

export const createChannelSchema = z.object({
  name: z.string().min(1, 'Channel name is required').max(50, 'Channel name must be less than 50 characters'),
  description: z.string().max(200, 'Description must be less than 200 characters').optional(),
  isPrivate: z.boolean().optional(),
});

export const updateChannelSchema = z.object({
  name: z.string().min(1, 'Channel name is required').max(50, 'Channel name must be less than 50 characters').optional(),
  description: z.string().max(200, 'Description must be less than 200 characters').optional(),
  isPrivate: z.boolean().optional(),
});

export const validateRequest = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Validation error',
          details: error.errors,
        });
      }
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
};

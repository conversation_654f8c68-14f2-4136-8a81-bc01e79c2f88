# WhisperWave System Design Document

## Overview
WhisperWave is a real-time chat application with end-to-end encryption, file sharing, and voice/video call capabilities. This document outlines the system architecture, technology stack, and design considerations for building a secure, scalable, and high-performance communication platform using modern web technologies.

## Architecture

### High-Level Architecture
```mermaid
graph TB
    subgraph "Client (Next.js)"
        A[React UI Components]
        B[State Management - Redux]
        C[WebSocket Client]
        D[WebRTC for Calls]
        E[Encryption Module]
    end

    subgraph "Backend (Node.js/Express)"
        F[API Gateway]
        G[Authentication Service]
        H[WebSocket Server]
        I[File Storage Service]
        J[Signaling Server for WebRTC]
    end

    subgraph "Database (PostgreSQL)"
        K[User Data]
        L[Message Store - Encrypted]
        M[Channel Data]
    end

    A --> B
    B --> C
    C --> H
    A --> D
    D --> J
    A --> E

    F --> G
    F --> H
    F --> I

    G --> K
    H --> L
    H --> M
    I --> L
```

### Technology Stack
- **Frontend**: Next.js 14 with TypeScript
- **Backend**: Node.js with Express and Socket.IO
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with bcrypt for password hashing
- **Deployment**: Vercel for frontend, Docker for backend on AWS/DigitalOcean
- **Styling**: Tailwind CSS with Shadcn UI
- **Real-time Communication**: Socket.IO for messaging, WebRTC for calls

### System Architecture Patterns
- **Microservices-inspired Backend**: Authentication, WebSocket, and File services will be separate modules, allowing for independent scaling and development.
- **Client-Side Encryption**: End-to-end encryption will be handled on the client-side to ensure message privacy. The server will never have access to unencrypted message content.

## Components and Interfaces

### Core Components

#### 1. User Authentication Service
```typescript
interface IUser {
  id: string;
  username: string;
  email: string;
  avatarUrl: string;
  onlineStatus: 'online' | 'offline' | 'away';
}

interface IAuthService {
  register(user: Pick<IUser, 'email' | 'username'>, password: string): Promise<{ user: IUser, token: string }>;
  login(email: string, password: string): Promise<{ user: IUser, token: string }>;
  validateToken(token: string): Promise<IUser | null>;
}
```

### API Endpoints Structure
#### Authentication Endpoints
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Authenticate a user and get a JWT token
- `GET /api/auth/me` - Get current user profile (requires token)

#### Messaging Endpoints (WebSocket Events)
- `sendMessage` - Send a message to a channel
- `joinChannel` - Join a specific chat channel
- `leaveChannel` - Leave a chat channel
- `typing` - Broadcast typing indicator to channel members

## Data Models

### Database Schema
```prisma
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  avatarUrl String?
  channels  Channel[] @relation("ChannelMembers")
  messages  Message[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Channel {
  id        String   @id @default(cuid())
  name      String
  isPrivate Boolean  @default(false)
  members   User[]   @relation("ChannelMembers")
  messages  Message[]
  createdAt DateTime @default(now())
}

model Message {
  id        String   @id @default(cuid())
  content   String   // Encrypted content
  channel   Channel  @relation(fields: [channelId], references: [id])
  channelId String
  author    User     @relation(fields: [authorId], references: [id])
  authorId  String
  createdAt DateTime @default(now())
  fileUrl   String?  // For file sharing
}
```

## Error Handling

### Error Response Structure
```typescript
interface IErrorResponse {
  statusCode: number;
  message: string;
  details?: Record<string, any>;
}

// Centralized error handling middleware in Express will format errors into this structure.
```

## Performance Considerations
- **Message Batching**: Sending multiple messages in a single WebSocket event to reduce network overhead.
- **Lazy Loading**: Use Next.js dynamic imports to lazy-load components and libraries.
- **Optimized Database Queries**: Use Prisma to generate efficient database queries and implement pagination for message history.

## Security Considerations
- **End-to-End Encryption**: Use a standard library like `libsodium` for client-side encryption.
- **Secure Authentication**: Implement JWT with short-lived access tokens and refresh tokens.
- **Input Validation**: Use a library like `zod` to validate all API inputs and prevent malicious data.
- **CORS Configuration**: Restrict API access to the Next.js frontend domain only.

## Testing Strategy
- **Unit Tests**: Use Jest and React Testing Library to test individual components and utility functions.
- **Integration Tests**: Test API endpoints with Supertest, ensuring proper database interaction.
- **End-to-End Tests**: Use Cypress to simulate user flows like registration, login, messaging, and file sharing.

## Monitoring and Analytics
- **Logging**: Use a logging library like Winston to log application events and errors.
- **Performance Monitoring**: Integrate a service like Sentry or Datadog for real-time error tracking and performance monitoring.

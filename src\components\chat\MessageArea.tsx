"use client";
import React, { useEffect, useRef } from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchMessages, addMessage, setUserTyping } from '@/store/slices/messageSlice';
import socketService from '@/lib/socket';
import encryptionService from '@/lib/encryptionService';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import TypingIndicator from './TypingIndicator';
import EncryptionStatus from './EncryptionStatus';

const MessageArea: React.FC = () => {
  const { currentChannel, messages, typingUsers, isConnected } = useAppSelector((state) => state.messages);
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const hasSetupListeners = useRef(false);

  // Fetch messages when channel changes and join the channel room
  useEffect(() => {
    if (currentChannel?.id && isConnected) {
      dispatch(fetchMessages({ channelId: currentChannel.id, page: 1 }));
      // Join the channel room for real-time updates
      socketService.joinChannel(currentChannel.id);
      
      return () => {
        // Leave the channel room when switching channels
        socketService.leaveChannel(currentChannel.id);
      };
    }
  }, [currentChannel?.id, isConnected, dispatch]);

  // Setup Socket.IO event listeners
  useEffect(() => {
    if (!isConnected) return;

    const socket = socketService.getSocket();
    if (!socket) return;

    console.log('Setting up MessageArea socket listeners');

    // Listen for incoming messages
    const handleMessageReceived = async (data: { message: any; channelId: string }) => {
      console.log('Message received:', data);
      const { content, iv, senderPublicKey } = data.message;
      
      // Only process messages for the current channel
      if (currentChannel?.id && data.channelId === currentChannel.id) {
        // Check if message is encrypted (has iv and senderPublicKey)
        if (iv && senderPublicKey) {
          try {
            const decryptedMessage = await encryptionService.decryptMessageFromChannel(
              data.channelId,
              content,
              iv,
              senderPublicKey
            );
            if (decryptedMessage) {
              dispatch(addMessage({ ...data.message, content: decryptedMessage }));
            } else {
              console.error('Failed to decrypt message');
              // Fallback to plain text if decryption fails
              dispatch(addMessage(data.message));
            }
          } catch (error) {
            console.error('Failed to decrypt message:', error);
            // Fallback to plain text if decryption fails
            dispatch(addMessage(data.message));
          }
        } else {
          // Message is plain text, no decryption needed
          dispatch(addMessage(data.message));
        }
      }
    };

    // Listen for typing indicators
    const handleUserTyping = (data: { userId: string; username: string; channelId: string; isTyping: boolean }) => {
      // Don't show typing indicator for current user
      if (data.userId !== user?.id) {
        dispatch(setUserTyping(data));
      }
    };

    // Remove any existing listeners first
    socketService.removeListener('messageReceived');
    socketService.removeListener('userTyping');

    // Add new listeners
    socketService.onMessageReceived(handleMessageReceived);
    socketService.onUserTyping(handleUserTyping);

    return () => {
      console.log('Cleaning up MessageArea socket listeners');
      socketService.removeListener('messageReceived');
      socketService.removeListener('userTyping');
    };
  }, [isConnected, dispatch, user?.id, currentChannel?.id]);

  if (!currentChannel) {
    return (
      <div className="flex-1 flex items-center justify-center bg-white">
        <div className="text-center text-gray-500">
          <p>No channel selected</p>
        </div>
      </div>
    );
  }

  const channelMessages = messages[currentChannel.id] || [];
  const currentChannelTypingUsers = typingUsers.filter(u => u.channelId === currentChannel.id);

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* Channel Header */}
      <div className="border-b border-gray-200 px-6 py-4 bg-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              # {currentChannel.name}
            </h2>
            {currentChannel.description && (
              <p className="text-sm text-gray-600 mt-1">{currentChannel.description}</p>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              {currentChannel.members.length} member{currentChannel.members.length !== 1 ? 's' : ''}
            </div>
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-1 text-sm ${
                isConnected ? 'text-green-600' : 'text-red-600'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
              </div>
              <EncryptionStatus isEncrypted={false} />
            </div>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 flex flex-col min-h-0">
        <MessageList messages={channelMessages} />
        
        {/* Typing Indicator */}
        {currentChannelTypingUsers.length > 0 && (
          <div className="px-6 py-2">
            <TypingIndicator users={currentChannelTypingUsers} />
          </div>
        )}
        
        {/* Message Input */}
        <div className="border-t border-gray-200 bg-white">
          <MessageInput channelId={currentChannel.id} />
        </div>
      </div>
    </div>
  );
};

export default MessageArea;

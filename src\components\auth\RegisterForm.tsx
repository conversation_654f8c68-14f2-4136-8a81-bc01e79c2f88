"use client";
import React, { useEffect, useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { registerUser, clearError } from "@/store/slices/authSlice";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form } from "@/components/ui/form";

const registerFormSchema = z.object({
  email: z.string().email("Invalid email format"),
  username: z
    .string()
    .min(3, "Username must be at least 3 characters")
    .max(20, "Username must be less than 20 characters"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type RegisterFormInputs = z.infer<typeof registerFormSchema>;

const RegisterForm: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<RegisterFormInputs>({
    resolver: zodResolver(registerFormSchema),
    defaultValues: {
      email: "",
      username: "",
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: RegisterFormInputs) => {
    const result = await dispatch(registerUser({
      email: data.email,
      username: data.username,
      password: data.password,
    }));

    if (registerUser.fulfilled.match(result)) {
      setRegistrationSuccess(true);
      reset();
    }
  };

  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  if (registrationSuccess) {
    return (
      <div className="max-w-md mx-auto text-center">
        <h2 className="text-lg font-semibold mb-4 text-green-600">
          Registration Successful!
        </h2>
        <p className="mb-4">
          Your account has been created. You can now log in with your credentials.
        </p>
        <Button onClick={() => setRegistrationSuccess(false)}>
          Back to Registration
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto">
      <h2 className="text-lg font-semibold mb-4">Register</h2>
      <Form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <label>Email</label>
          <Controller
            name="email"
            control={control}
            render={({ field }) => <Input placeholder="Email" {...field} />}
          />
          {errors.email && (
            <span className="text-red-500 text-sm">
              {errors.email.message}
            </span>
          )}
        </div>

        <div className="mb-4">
          <label>Username</label>
          <Controller
            name="username"
            control={control}
            render={({ field }) => <Input placeholder="Username" {...field} />}
          />
          {errors.username && (
            <span className="text-red-500 text-sm">
              {errors.username.message}
            </span>
          )}
        </div>

        <div className="mb-4">
          <label>Password</label>
          <Controller
            name="password"
            control={control}
            render={({ field }) => (
              <Input type="password" placeholder="Password" {...field} />
            )}
          />
          {errors.password && (
            <span className="text-red-500 text-sm">
              {errors.password.message}
            </span>
          )}
        </div>

        <div className="mb-4">
          <label>Confirm Password</label>
          <Controller
            name="confirmPassword"
            control={control}
            render={({ field }) => (
              <Input type="password" placeholder="Confirm Password" {...field} />
            )}
          />
          {errors.confirmPassword && (
            <span className="text-red-500 text-sm">
              {errors.confirmPassword.message}
            </span>
          )}
        </div>

        {error && <span className="text-red-500 text-sm">{error}</span>}

        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? "Creating Account..." : "Register"}
        </Button>
      </Form>
    </div>
  );
};

export default RegisterForm;

import express from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  sendFriendRequest,
  getFriendRequests,
  manageFriendRequest,
  getFriends,
  removeFriend,
  blockUser,
  unblockUser,
  getBlockedUsers
} from '../controllers/friendsController';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Friend request routes
router.post('/requests', sendFriendRequest);
router.get('/requests', getFriendRequests);
router.post('/requests/manage', manageFriendRequest);

// Friends routes
router.get('/', getFriends);
router.delete('/:friendshipId', removeFriend);

// Block/unblock routes
router.post('/block', blockUser);
router.post('/unblock', unblockUser);
router.get('/blocked', getBlockedUsers);

export default router;

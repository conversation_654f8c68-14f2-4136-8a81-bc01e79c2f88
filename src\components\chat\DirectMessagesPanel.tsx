'use client';

import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchConversations, fetchConversation, sendDirectMessage, setCurrentConversationUser, decryptMessageContent, addDirectMessage } from '@/store/slices/directMessagesSlice';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, Heart, Shield, ShieldCheck, Send, ShieldOff } from 'lucide-react';
import encryptionService from '@/lib/encryptionService';
import socketService from '@/lib/socket';

const DirectMessagesPanel = () => {
  const dispatch = useAppDispatch();
  const { conversations, currentConversation, currentConversationUserId, isLoading, error } = useAppSelector(state => state.directMessages);
  const { user, token } = useAppSelector(state => state.auth);
  const { isConnected } = useAppSelector(state => state.messages);

  const [messageContent, setMessageContent] = useState('');
  const [decryptedMessages, setDecryptedMessages] = useState<Map<string, string>>(new Map());
  const [encryptionReady, setEncryptionReady] = useState(false);
  const [encryptionEnabled, setEncryptionEnabled] = useState(true);
  const [userPublicKeys, setUserPublicKeys] = useState<Map<string, string>>(new Map());

  useEffect(() => {
    if (token) {
      dispatch(fetchConversations());
      // Initialize encryption service with user ID
      console.log('🔑 Initializing encryption keys for user:', user?.username, 'ID:', user?.id);
      encryptionService.initializeUserKeys(user?.id).then(async (keys) => {
        console.log('✅ Encryption keys initialized:', {
          publicKeyLength: keys.publicKey.length,
          hasPrivateKey: 'Private key should be stored in encryptionService internally'
        });
        setEncryptionReady(true);
        // Upload user's public key to server
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/users/public-key`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({ publicKey: keys.publicKey }),
          });
          if (response.ok) {
            console.log('✅ Public key uploaded to server');
          } else {
            console.warn('⚠️ Failed to upload public key:', response.status);
          }
        } catch (error) {
          console.warn('❌ Failed to upload public key:', error);
        }
      }).catch(error => {
        console.error('❌ Failed to initialize encryption:', error);
      });
    }
  }, [dispatch, token]);

  // Debug current conversation structure
  useEffect(() => {
    if (currentConversation.length > 0) {
      console.log('🟢 Current conversation messages:', currentConversation);
      if (currentConversation.length > 0) {
        console.log('🟢 First message sender:', currentConversation[0].sender);
        console.log('🟢 First message receiver:', currentConversation[0].receiver);
      }
    }
  }, [currentConversation]);

  // Decrypt messages when conversation loads
  useEffect(() => {
    console.log('🔄 Decryption effect triggered:', {
      conversationLength: currentConversation.length,
      currentConversationUserId,
      shouldDecrypt: currentConversation.length > 0 && currentConversationUserId
    });
    
    if (currentConversation.length > 0 && currentConversationUserId) {
      const decryptMessages = async () => {
        // Debug encryption service state before attempting decryption
        console.log('🔍 Debugging encryption service state before decryption:');
        encryptionService.debugKeyState();
        
        const newDecryptedMessages = new Map(decryptedMessages);
        
        for (const message of currentConversation) {
          if (!newDecryptedMessages.has(message.id)) {
            try {
              console.log('🔐 Decrypting existing message:', {
                messageId: message.id,
                content: message.content.substring(0, 100),
                isEncrypted: message.content.startsWith('{"encrypted"'),
                currentConversationUserId
              });
              const decryptedContent = await decryptMessageContent(message, currentConversationUserId);
              console.log('✅ Decryption successful for:', message.id, decryptedContent.substring(0, 50));
              newDecryptedMessages.set(message.id, decryptedContent);
            } catch (error) {
              console.error('❌ Failed to decrypt message:', message.id, error);
              newDecryptedMessages.set(message.id, '[Decryption failed]');
            }
          }
        }
        
        setDecryptedMessages(newDecryptedMessages);
      };
      
      decryptMessages();
    }
  }, [currentConversation, currentConversationUserId]);

  // Set up real-time direct message listeners
  useEffect(() => {
    if (!isConnected) return;

    const socket = socketService.getSocket();
    if (!socket) return;

    // Handle incoming direct messages
    const handleDirectMessageReceived = async (message: any) => {
      console.log('🔵 Direct message received:', message);
      console.log('🔵 Message sender object:', message.sender);
      console.log('🔵 Message receiver object:', message.receiver);
      dispatch(addDirectMessage(message));
      
      try {
        const decryptedContent = await decryptMessageContent(message, message.senderId === user?.id ? message.receiverId : message.senderId);
        setDecryptedMessages(prev => {
          const newMap = new Map(prev);
          newMap.set(message.id, decryptedContent);
          return newMap;
        });
      } catch (error) {
        console.error('Failed to decrypt received message:', error);
        setDecryptedMessages(prev => new Map(prev.set(message.id, message.content)));
      }
    };

    // Handle sent direct message confirmation
    const handleDirectMessageSent = async (message: any) => {
      dispatch(addDirectMessage(message));
      
      try {
        const decryptedContent = await decryptMessageContent(message, message.receiverId);
        setDecryptedMessages(prev => {
          const newMap = new Map(prev);
          newMap.set(message.id, decryptedContent);
          return newMap;
        });
      } catch (error) {
        console.error('Failed to decrypt sent message:', error);
        setDecryptedMessages(prev => new Map(prev.set(message.id, message.content)));
      }
    };

    // Remove existing listeners first - same as channels
    socketService.removeListener('directMessageReceived');
    socketService.removeListener('directMessageSent');

    // Add new listeners
    socketService.onDirectMessageReceived(handleDirectMessageReceived);
    socketService.onDirectMessageSent(handleDirectMessageSent);

    return () => {
      socketService.removeListener('directMessageReceived');
      socketService.removeListener('directMessageSent');
    };
  }, [isConnected, dispatch, currentConversationUserId, user?.id]);

  const handleUserSelect = async (otherUserId: string) => {
    dispatch(setCurrentConversationUser(otherUserId));
    dispatch(fetchConversation({ otherUserId }));
    
    // Fetch the other user's public key for encryption
    if (encryptionEnabled && !userPublicKeys.has(otherUserId)) {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/users/${otherUserId}/public-key`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (response.ok) {
          const { publicKey } = await response.json();
          setUserPublicKeys(prev => new Map(prev.set(otherUserId, publicKey)));
        } else if (response.status === 404) {
          console.warn(`User ${otherUserId} has no public key set - using plain text messages`);
          // Set a placeholder to prevent repeated requests
          setUserPublicKeys(prev => new Map(prev.set(otherUserId, 'NO_KEY')));
        } else {
          console.warn(`Failed to fetch public key for user ${otherUserId}: ${response.status}`);
        }
      } catch (error) {
        console.error('Failed to fetch user public key:', error);
        // Set placeholder to prevent repeated requests
        setUserPublicKeys(prev => new Map(prev.set(otherUserId, 'NO_KEY')));
      }
    }
    
    // Mark messages as read - optionally could be done here
  };

  const handleSendMessage = async () => {
    if (messageContent.trim() && currentConversationUserId) {
      // Only use public key for encryption if it's valid (not our placeholder)
      const storedPublicKey = userPublicKeys.get(currentConversationUserId);
      let finalContent = messageContent;
      
      // Encrypt message if possible
      if (encryptionEnabled && storedPublicKey && storedPublicKey !== 'NO_KEY') {
        try {
          const encryptedData = await encryptionService.encryptDirectMessage(
            currentConversationUserId, 
            messageContent, 
            storedPublicKey
          );
          if (encryptedData) {
            finalContent = JSON.stringify({
              encrypted: true,
              content: encryptedData.encryptedContent,
              iv: encryptedData.iv,
              senderPublicKey: encryptedData.senderPublicKey
            });
          }
        } catch (error) {
          console.warn('Failed to encrypt direct message, sending as plain text:', error);
        }
      }
      
      // Send via socket instead of HTTP API
      const socket = socketService.getSocket();
      if (socket) {
        socket.emit('sendDirectMessage', {
          receiverId: currentConversationUserId,
          content: finalContent,
          messageType: 'TEXT'
        });
      } else {
        // Fallback to HTTP API if socket not available
        dispatch(sendDirectMessage({ 
          receiverId: currentConversationUserId, 
          content: finalContent
        }));
      }
      
      setMessageContent('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex h-full">
      <div className="w-1/3 border-r overflow-y-auto">
        <div className="p-4">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            Direct Messages
          </h2>
          <div className="space-y-2">
            {Array.isArray(conversations) ? conversations.map(convo => (
              <Card key={convo.user.id} className="cursor-pointer hover:bg-gray-100" onClick={() => handleUserSelect(convo.user.id)}>
                <CardContent className="flex items-center gap-3 p-2">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={convo.user.avatarUrl} alt={convo.user.username} />
                    <AvatarFallback>{convo.user.username.charAt(0).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium">{convo.user.username}</div>
                    <div className="text-xs text-gray-500 line-clamp-1">{convo.lastMessage?.content || 'No messages yet'}</div>
                  </div>
                  {convo.unreadCount > 0 && (
                    <div className="bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                      {convo.unreadCount}
                    </div>
                  )}
                </CardContent>
              </Card>
            )) : (
              <div className="text-center py-4 text-gray-500">
                {isLoading ? 'Loading conversations...' : 'No conversations yet'}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="w-2/3 flex flex-col">
        <div className="border-b p-4">
          {currentConversationUserId ? (
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold">Conversation with {Array.isArray(conversations) ? conversations.find(convo => convo.user.id === currentConversationUserId)?.user.username : 'User'}</h3>
              <div className="flex items-center gap-2">
                {(() => {
                  const storedPublicKey = userPublicKeys.get(currentConversationUserId);
                  const hasValidKey = storedPublicKey && storedPublicKey !== 'NO_KEY';
                  const canEncrypt = encryptionEnabled && hasValidKey;
                  
                  return (
                    <>
                      <button 
                        onClick={() => {
                          encryptionService.clearStoredKeys();
                          alert('Keys cleared! Refresh the page to generate new keys.');
                        }}
                        className="flex items-center gap-1 px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-700 hover:bg-red-200"
                        title="Clear stored encryption keys (for debugging)"
                      >
                        <span>🗑️ Clear Keys</span>
                      </button>
                      <button 
                        onClick={() => setEncryptionEnabled(!encryptionEnabled)}
                        disabled={!hasValidKey}
                        className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
                          !hasValidKey
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : canEncrypt 
                              ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                              : 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                        }`}
                        title={
                          !hasValidKey 
                            ? 'Other user has no encryption key - plain text only'
                            : canEncrypt 
                              ? 'End-to-end encryption enabled' 
                              : 'Encryption disabled - click to enable'
                        }
                      >
                        {!hasValidKey ? (
                          <>
                            <ShieldOff className="w-3 h-3" />
                            <span>No key</span>
                          </>
                        ) : canEncrypt ? (
                          <>
                            <ShieldCheck className="w-3 h-3" />
                            <span>Encrypted</span>
                          </>
                        ) : (
                          <>
                            <Shield className="w-3 h-3" />
                            <span>Plain text</span>
                          </>
                        )}
                      </button>
                    </>
                  );
                })()}
              </div>
            </div>
          ) : (
            <div className="text-gray-600">Select a conversation to start messaging.</div>
          )}
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {currentConversation.map(msg => {
            const decryptedContent = decryptedMessages.get(msg.id) || msg.content;
            const isEncrypted = msg.content.startsWith('{"encrypted"');
            const isMine = msg.senderId === user?.id;
            
            console.log('🔍 Message debug:', {
              messageId: msg.id,
              senderId: msg.senderId,
              currentUserId: user?.id,
              isMine,
              senderUsername: msg.sender?.username,
              content: decryptedContent.substring(0, 20) + '...'
            });
            
            return (
              <div key={msg.id} className={`flex items-start gap-3 my-2 ${msg.senderId === user?.id ? 'justify-end' : 'justify-start'}`}>
                {msg.senderId !== user?.id && (
                  <Avatar className="w-8 h-8 mt-1">
                    <AvatarImage src={msg.sender?.avatarUrl} alt={msg.sender?.username || 'User'} />
                    <AvatarFallback>{msg.sender?.username?.charAt(0).toUpperCase() || '?'}</AvatarFallback>
                  </Avatar>
                )}
                <div className={`max-w-xs break-words p-3 rounded-lg shadow-sm border ${
                  msg.senderId === user?.id 
                    ? 'bg-blue-500 text-white rounded-br-none border-blue-500' 
                    : 'bg-white text-black rounded-bl-none border-gray-200'
                }`}>
                  <div className="flex items-start gap-2">
                    <div className="flex-1">
                      {decryptedContent}
                    </div>
                    {isEncrypted && (
                      <ShieldCheck className="w-3 h-3 text-green-400 flex-shrink-0 mt-0.5" title="Encrypted message" />
                    )}
                  </div>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-xs opacity-70">
                      {new Date(msg.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                    {msg.senderId === user?.id && msg.isRead && (
                      <Heart className="w-3 h-3 text-red-300" title="Read" />
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {currentConversationUserId && (
          <div className="p-4 border-t">
            <div className="flex items-center gap-2">
              <Input
                placeholder="Type a message..."
                value={messageContent}
                onChange={e => setMessageContent(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1"
              />
              <Button 
                onClick={handleSendMessage}
                disabled={!messageContent.trim()}
                size="sm"
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DirectMessagesPanel;


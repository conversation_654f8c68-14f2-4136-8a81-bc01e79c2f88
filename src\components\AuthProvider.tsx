"use client";
import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { getCurrentUser, logout } from '@/store/slices/authSlice';
import { isTokenExpired } from '@/lib/jwt';

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { token, isAuthenticated, user, isLoading } = useAppSelector((state) => state.auth);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeAuth = async () => {
      // If we have a token but no user, validate it
      if (token && !isAuthenticated && !user && !isLoading) {
        // First check if token is expired client-side
        if (isTokenExpired(token)) {
          console.log('Token is expired, logging out');
          dispatch(logout());
          setIsInitialized(true);
          return;
        }

        // Token looks valid, verify with server
        try {
          await dispatch(getCurrentUser()).unwrap();
          console.log('Token validation successful');
        } catch (error) {
          console.error('Token validation failed:', error);
          // Token is invalid, clear it
          dispatch(logout());
        }
      }
      setIsInitialized(true);
    };

    initializeAuth();
  }, [dispatch, token, isAuthenticated, user, isLoading]);

  // Show loading screen while checking authentication
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent text-blue-600 rounded-full mb-4" role="status" aria-label="loading">
            <span className="sr-only">Loading...</span>
          </div>
          <p className="text-gray-600">Initializing authentication...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;

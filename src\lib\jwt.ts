// JWT utility functions for client-side token handling

interface JWTPayload {
  userId: string;
  iat: number;
  exp: number;
}

/**
 * Decode JWT payload without verification (client-side only)
 * Note: This should NOT be used for security verification, only for reading token data
 */
export function decodeJWTPayload(token: string): JWTPayload | null {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = parts[1];
    const decodedPayload = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decodedPayload) as JWTPayload;
  } catch (error) {
    console.error('Error decoding JWT payload:', error);
    return null;
  }
}

/**
 * Check if a JWT token is expired
 * Note: This is for client-side convenience only, server-side validation is still required
 */
export function isTokenExpired(token: string): boolean {
  const payload = decodeJWTPayload(token);
  if (!payload || !payload.exp) {
    return true;
  }

  const currentTime = Math.floor(Date.now() / 1000);
  return payload.exp < currentTime;
}

/**
 * Check if a JWT token will expire soon (within the next 5 minutes)
 */
export function isTokenExpiringSoon(token: string, thresholdMinutes: number = 5): boolean {
  const payload = decodeJWTPayload(token);
  if (!payload || !payload.exp) {
    return true;
  }

  const currentTime = Math.floor(Date.now() / 1000);
  const thresholdTime = currentTime + (thresholdMinutes * 60);
  return payload.exp < thresholdTime;
}

/**
 * Get token expiration time as a Date object
 */
export function getTokenExpirationDate(token: string): Date | null {
  const payload = decodeJWTPayload(token);
  if (!payload || !payload.exp) {
    return null;
  }

  return new Date(payload.exp * 1000);
}

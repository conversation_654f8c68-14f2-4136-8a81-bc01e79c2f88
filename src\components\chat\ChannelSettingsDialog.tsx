"use client";
import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { updateChannel, deleteChannel, clearError } from '@/store/slices/channelSlice';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { X, Loader2, Trash2, Users, Settings } from 'lucide-react';

interface ChannelSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  channel: {
    id: string;
    name: string;
    description?: string;
    isPrivate: boolean;
    ownerId: string;
    memberCount?: number;
  } | null;
}

const ChannelSettingsDialog: React.FC<ChannelSettingsDialogProps> = ({ 
  open, 
  onOpenChange, 
  channel 
}) => {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.channels);
  const { user } = useAppSelector((state) => state.auth);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPrivate: false,
  });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Update form data when channel changes
  useEffect(() => {
    if (channel) {
      setFormData({
        name: channel.name,
        description: channel.description || '',
        isPrivate: channel.isPrivate,
      });
    }
  }, [channel]);

  const isOwner = channel && user && channel.ownerId === user.id;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!channel || !formData.name.trim()) return;

    try {
      await dispatch(updateChannel({
        channelId: channel.id,
        updates: {
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          isPrivate: formData.isPrivate,
        }
      })).unwrap();
      
      onOpenChange(false);
    } catch (error) {
      // Error is handled by Redux
    }
  };

  const handleDelete = async () => {
    if (!channel) return;

    try {
      await dispatch(deleteChannel(channel.id)).unwrap();
      setShowDeleteConfirm(false);
      onOpenChange(false);
    } catch (error) {
      // Error is handled by Redux
    }
  };

  const handleClose = () => {
    setShowDeleteConfirm(false);
    dispatch(clearError());
    onOpenChange(false);
  };

  const handleChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!channel) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Channel Settings
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        {showDeleteConfirm ? (
          <div className="space-y-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Delete Channel
              </h3>
              <p className="text-sm text-red-700 mb-4">
                Are you sure you want to delete "{channel.name}"? This action cannot be undone and will remove all messages in this channel.
              </p>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Channel
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Channel Info */}
            <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Channel Info</span>
                <div className="flex items-center text-xs text-gray-500">
                  <Users className="h-3 w-3 mr-1" />
                  {channel.memberCount || 0} members
                </div>
              </div>
              <p className="text-xs text-gray-600">
                Created by: {channel.ownerId === user?.id ? 'You' : 'Another user'}
              </p>
            </div>

            {/* Form Fields */}
            <div className="space-y-2">
              <Label htmlFor="channel-name">Channel Name *</Label>
              <Input
                id="channel-name"
                type="text"
                placeholder="Enter channel name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                maxLength={50}
                disabled={isLoading || !isOwner}
                required
              />
              <p className="text-xs text-gray-500">
                {formData.name.length}/50 characters
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="channel-description">Description</Label>
              <Input
                id="channel-description"
                type="text"
                placeholder="Enter channel description (optional)"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                maxLength={200}
                disabled={isLoading || !isOwner}
              />
              <p className="text-xs text-gray-500">
                {formData.description.length}/200 characters
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="channel-private"
                checked={formData.isPrivate}
                onChange={(e) => handleChange('isPrivate', e.target.checked)}
                disabled={isLoading || !isOwner}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <Label htmlFor="channel-private" className="text-sm">
                Private Channel
              </Label>
            </div>
            <p className="text-xs text-gray-500">
              Private channels require an invitation to join
            </p>

            {!isOwner && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-700">
                  Only the channel owner can modify these settings.
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              {isOwner && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setShowDeleteConfirm(true)}
                  disabled={isLoading}
                  className="flex items-center"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Channel
                </Button>
              )}
              
              <div className="flex space-x-2 ml-auto">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                {isOwner && (
                  <Button
                    type="submit"
                    disabled={isLoading || !formData.name.trim()}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </Button>
                )}
              </div>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ChannelSettingsDialog;

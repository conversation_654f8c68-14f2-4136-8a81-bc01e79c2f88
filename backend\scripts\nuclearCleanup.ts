import prisma from '../src/utils/database';

async function nuclearCleanup() {
  console.log('🔥 NUCLEAR CLEANUP - Resetting everything...');
  
  try {
    // 1. Delete ALL messages (channel and direct messages)
    const channelMessagesResult = await prisma.message.deleteMany({});
    console.log(`✅ Deleted ${channelMessagesResult.count} channel messages`);

    const directMessagesResult = await prisma.directMessage.deleteMany({});
    console.log(`✅ Deleted ${directMessagesResult.count} direct messages`);

    // 2. Delete ALL conversations
    await prisma.$executeRaw`DELETE FROM conversations`;
    console.log(`✅ Deleted all conversations`);

    // 3. Set all users offline and clear any cached data
    const usersResult = await prisma.user.updateMany({
      data: {
        isOnline: false,
        status: 'OFFLINE',
        lastSeen: new Date()
      }
    });
    console.log(`✅ Set ${usersResult.count} users to offline status`);

    // 4. Clear any user public keys if they exist
    try {
      await prisma.user.updateMany({
        data: {
          // Clear any encryption-related fields if they exist
          // Note: Only do this if these fields exist in your schema
        }
      });
    } catch (e) {
      // Fields might not exist, that's fine
    }

    console.log('🎉 NUCLEAR CLEANUP COMPLETED!');
    console.log('📊 Summary:');
    console.log(`   - Channel messages: ${channelMessagesResult.count} deleted`);
    console.log(`   - Direct messages: ${directMessagesResult.count} deleted`);
    console.log(`   - Users reset: ${usersResult.count}`);
    console.log(`   - All conversations cleared`);
    console.log('');
    console.log('🚨 NEXT STEPS:');
    console.log('1. Run the browser console script to clear all encryption keys');
    console.log('2. Refresh the browser completely');
    console.log('3. The app will start fresh with no encrypted content');

  } catch (error) {
    console.error('❌ Error during nuclear cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
  
  process.exit(0);
}

nuclearCleanup();

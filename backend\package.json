{"name": "whisperwave-backend", "version": "1.0.0", "description": "WhisperWave backend server with real-time messaging", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "tsx src/seed.ts"}, "keywords": ["chat", "websocket", "real-time", "messaging", "encryption"], "author": "WhisperWave Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "@prisma/client": "^5.7.1", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "typescript": "^5.3.3", "tsx": "^4.6.2", "jest": "^29.7.0", "@types/jest": "^29.5.8", "prisma": "^5.7.1"}}
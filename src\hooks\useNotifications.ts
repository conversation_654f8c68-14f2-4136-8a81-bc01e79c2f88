import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { addDirectMessage } from '@/store/slices/directMessagesSlice';
import { fetchFriends } from '@/store/slices/friendsSlice';
import socketService from '@/lib/socket';
import notificationService from '@/lib/notificationService';

export const useNotifications = () => {
  const dispatch = useAppDispatch();
  const { user, token } = useAppSelector(state => state.auth);
  const { currentConversationUserId } = useAppSelector(state => state.directMessages);

  // Handle direct message received
  const handleDirectMessageReceived = useCallback((data: any) => {
    console.log('🔔 Direct message received via socket:', {
      messageId: data.id,
      senderId: data.senderId,
      receiverId: data.receiverId,
      content: data.content?.substring(0, 50) + '...'
    });
    
    // Add message to store
    dispatch(addDirectMessage(data));
    
    // Show notification if message is from someone else and not in current conversation
    if (data.senderId !== user?.id) {
      // Only show notification if not currently in conversation with this user
      // or if the page is not focused
      const shouldShowNotification = currentConversationUserId !== data.senderId || document.hidden;
      
      if (shouldShowNotification && notificationService) {
        // The socket service already handles notification display
        // but we can add additional logic here if needed
        console.log('DM notification triggered for:', data.sender?.username);
      }
    }
  }, [dispatch, user?.id, currentConversationUserId]);

  // Handle direct message sent (our own messages)
  const handleDirectMessageSent = useCallback((data: any) => {
    // Add sent message to store
    dispatch(addDirectMessage(data));
  }, [dispatch]);

  // Handle friend request received
  const handleFriendRequestReceived = useCallback((data: any) => {
    console.log('Friend request received from:', data.requester?.username);
    // The socket service already handles notification display
    // You could add additional UI updates here, like updating a badge count
  }, []);

  // Handle friend request status updated
  const handleFriendRequestUpdated = useCallback((data: any) => {
    console.log('Friend request updated:', data);
    // Refresh friends list when request is accepted
    if (data.status === 'ACCEPTED') {
      dispatch(fetchFriends());
      // The socket service already handles notification display for accepted requests
    }
  }, [dispatch]);

  useEffect(() => {
    if (!token || !user) return;

    // Request notification permission on first load
    if (notificationService) {
      notificationService.requestPermission();
    }

    // Set up socket event listeners with our handlers
    socketService.onDirectMessageReceived(handleDirectMessageReceived);
    socketService.onDirectMessageSent(handleDirectMessageSent);
    socketService.onFriendRequestReceived(handleFriendRequestReceived);
    socketService.onFriendRequestUpdated(handleFriendRequestUpdated);

    // Cleanup on unmount
    return () => {
      // Remove specific listeners to prevent memory leaks
      socketService.removeListener('directMessageReceived');
      socketService.removeListener('directMessageSent');
      socketService.removeListener('friendRequestReceived');
      socketService.removeListener('friendRequestUpdated');
    };
  }, [token, user, handleDirectMessageReceived, handleDirectMessageSent, handleFriendRequestReceived, handleFriendRequestUpdated]);

  return {
    isNotificationSupported: notificationService.isSupported(),
    isNotificationPermissionGranted: notificationService.isPermissionGranted(),
    requestNotificationPermission: notificationService.requestPermission.bind(notificationService)
  };
};

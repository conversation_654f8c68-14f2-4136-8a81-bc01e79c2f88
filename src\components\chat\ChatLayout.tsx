"use client";
import React, { useState } from 'react';
import { useAppSelector } from '@/store/hooks';
import { useChannelRealtime } from '@/hooks/useChannelRealtime';
import { useNotifications } from '@/hooks/useNotifications';
import ChannelSidebar from './ChannelSidebar';
import InvitationsPanel from './InvitationsPanel';
import FriendsPanel from './FriendsPanel';
import DirectMessagesPanel from './DirectMessagesPanel';
import MessageArea from './MessageArea';
import UserPanel from './UserPanel';
import { Hash, Users, MessageSquare } from 'lucide-react';

type ViewType = 'channels' | 'friends' | 'directMessages';

const ChatLayout: React.FC = () => {
  const { currentChannel } = useAppSelector((state) => state.messages);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentView, setCurrentView] = useState<ViewType>('channels');
  
  // Set up real-time channel updates
  useChannelRealtime();
  
  // Set up notifications
  useNotifications();

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Sidebar */}
      <div className={`${
        sidebarOpen ? 'w-80' : 'w-16'
      } bg-gray-800 text-white flex flex-col transition-all duration-300`}>
        
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className={`${sidebarOpen ? 'block' : 'hidden'}`}>
              <h1 className="text-xl font-bold">🌊 WhisperWave</h1>
              <p className="text-gray-400 text-sm">Secure messaging</p>
            </div>
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              aria-label={sidebarOpen ? 'Collapse sidebar' : 'Expand sidebar'}
            >
              <svg 
                className={`w-5 h-5 transition-transform ${sidebarOpen ? 'rotate-180' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        {sidebarOpen && (
          <div className="px-2 py-3 border-b border-gray-700">
            <div className="flex space-x-1">
              <button
                onClick={() => setCurrentView('channels')}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                  currentView === 'channels'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700'
                }`}
              >
                <Hash className="w-4 h-4" />
                <span className="text-sm font-medium">Channels</span>
              </button>
              <button
                onClick={() => setCurrentView('friends')}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                  currentView === 'friends'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700'
                }`}
              >
                <Users className="w-4 h-4" />
                <span className="text-sm font-medium">Friends</span>
              </button>
              <button
                onClick={() => setCurrentView('directMessages')}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                  currentView === 'directMessages'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700'
                }`}
              >
                <MessageSquare className="w-4 h-4" />
                <span className="text-sm font-medium">DMs</span>
              </button>
            </div>
          </div>
        )}

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto">
          {currentView === 'channels' && <ChannelSidebar collapsed={!sidebarOpen} />}
          {currentView === 'friends' && sidebarOpen && (
            <FriendsPanel onStartDirectMessage={(friendId) => {
              setCurrentView('directMessages');
              // The DirectMessagesPanel will handle setting the current conversation
            }} />
          )}
        </div>

        {/* Invitations Panel - only show in channels view */}
        {currentView === 'channels' && (
          <div className="border-t border-gray-700">
            <InvitationsPanel collapsed={!sidebarOpen} />
          </div>
        )}

        {/* User Panel */}
        <div className="border-t border-gray-700">
          <UserPanel collapsed={!sidebarOpen} />
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {currentView === 'directMessages' ? (
          <DirectMessagesPanel />
        ) : currentChannel ? (
          <MessageArea />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-white">
            <div className="text-center">
              <div className="text-6xl mb-4">🌊</div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Welcome to WhisperWave</h2>
              <p className="text-gray-600 mb-4">
                {currentView === 'channels' && 'Select a channel to start messaging'}
                {currentView === 'friends' && 'Manage your friends and send friend requests'}
                {currentView === 'directMessages' && 'Select a conversation to start direct messaging'}
              </p>
              <div className="text-sm text-gray-500">
                <p>🔐 All messages are encrypted end-to-end</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatLayout;

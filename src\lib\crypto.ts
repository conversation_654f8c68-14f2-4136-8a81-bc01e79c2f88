const generateKeyPair = async (): Promise<CryptoKeyPair> => {
  const keyPair = await window.crypto.subtle.generateKey(
    {
      name: "ECDH",
      namedCurve: "P-256",
    },
    true,
    ["deriveKey"]
  );
  return keyPair;
};

const exportPublicKey = async (publicKey: CryptoKey): Promise<ArrayBuffer> => {
  const exported = await window.crypto.subtle.exportKey(
    "raw",
    publicKey
  );
  return exported;
};

const importPublicKey = async (rawKey: ArrayBuffer): Promise<CryptoKey> => {
  const publicKey = await window.crypto.subtle.importKey(
    "raw",
    rawKey,
    {
      name: "ECDH",
      namedCurve: "P-256"
    },
    true,
    []
  );
  return publicKey;
};

const deriveSharedKey = async (privateKey: CryptoKey, publicKey: CryptoKey): Promise<CryptoKey> => {
  const sharedSecret = await window.crypto.subtle.deriveKey(
    {
      name: "ECDH",
      public: publicKey
    },
    privateKey,
    {
      name: "AES-GCM",
      length: 256
    },
    false, // not extractable
    ["encrypt", "decrypt"]
  );
  return sharedSecret;
};

const encryptMessage = async (message: string, key: CryptoKey): Promise<{ ciphertext: ArrayBuffer; iv: Uint8Array }> => {
  const encoder = new TextEncoder();
  const encodedMessage = encoder.encode(message);
  const iv = window.crypto.getRandomValues(new Uint8Array(12));

  const ciphertext = await window.crypto.subtle.encrypt(
    {
      name: "AES-GCM",
      iv
    },
    key,
    encodedMessage
  );

  return { ciphertext, iv };
};

const decryptMessage = async (ciphertext: ArrayBuffer, iv: Uint8Array, key: CryptoKey): Promise<string> => {
  console.log('🔓 Starting decryption', {
    ciphertextLength: ciphertext.byteLength,
    ivLength: iv.byteLength,
    keyType: key.type,
    keyUsages: key.usages,
    keyAlgorithm: key.algorithm
  });
  
  try {
    const decrypted = await window.crypto.subtle.decrypt(
      {
        name: "AES-GCM",
        iv
      },
      key,
      ciphertext
    );

    console.log('✅ Decryption successful, decoding text...');
    const decoder = new TextDecoder();
    const result = decoder.decode(decrypted);
    console.log('✅ Text decoded successfully, length:', result.length);
    return result;
  } catch (error: any) {
    const errorInfo = {
      errorName: error?.name || 'UnknownError',
      errorMessage: error?.message || 'No error message available',
      errorCode: error?.code,
      keyValid: !!key,
      keyType: key?.type,
      keyAlgorithm: key?.algorithm ? JSON.stringify(key.algorithm) : 'unknown',
      keyUsages: key?.usages || [],
      ivValid: iv?.byteLength === 12,
      ivLength: iv?.byteLength,
      ciphertextValid: ciphertext?.byteLength > 0,
      ciphertextLength: ciphertext?.byteLength,
      timestamp: new Date().toISOString()
    };
    
    console.error('❌ Decryption failed in crypto.ts:', errorInfo);
    
    // Create a more descriptive error
    const descriptiveError = new Error(`Decryption failed: ${errorInfo.errorMessage}`);
    descriptiveError.name = errorInfo.errorName;
    throw descriptiveError;
  }
};

// Helper functions for converting between ArrayBuffer and base64
const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

const base64ToArrayBuffer = (base64: string): ArrayBuffer => {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
};

export { 
  generateKeyPair, 
  exportPublicKey, 
  importPublicKey, 
  deriveSharedKey, 
  encryptMessage, 
  decryptMessage,
  arrayBufferToBase64,
  base64ToArrayBuffer
};

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const sendMessageSchema = z.object({
  content: z.string().min(1).max(2000),
  channelId: z.string().cuid(),
  messageType: z.enum(['TEXT', 'FILE', 'IMAGE', 'SYSTEM']).default('TEXT'),
  fileUrl: z.string().url().optional(),
  fileName: z.string().optional(),
  fileSize: z.number().optional(),
  fileMimeType: z.string().optional()
});

const getMessagesSchema = z.object({
  channelId: z.string().cuid(),
  page: z.string().regex(/^\d+$/).transform(Number).default('1'),
  limit: z.string().regex(/^\d+$/).transform(Number).default('50')
});

// Get messages for a channel
export const getMessages = async (req: Request, res: Response) => {
  try {
    const { channelId, page, limit } = getMessagesSchema.parse({
      channelId: req.params.channelId,
      ...req.query
    });

    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is a member of the channel
    const channel = await prisma.channel.findFirst({
      where: {
        id: channelId,
        members: {
          some: { id: userId }
        }
      }
    });

    if (!channel) {
      return res.status(403).json({ error: 'Access denied to this channel' });
    }

    // Fetch messages with pagination
    const skip = (page - 1) * limit;
    const messages = await prisma.message.findMany({
      where: { channelId },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            avatarUrl: true,
            status: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalMessages = await prisma.message.count({
      where: { channelId }
    });

    const totalPages = Math.ceil(totalMessages / limit);

    res.json({
      messages: messages.reverse(), // Reverse to show oldest first
      pagination: {
        currentPage: page,
        totalPages,
        totalMessages,
        hasMore: page < totalPages
      }
    });

  } catch (error) {
    console.error('Error fetching messages:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Validation error',
        details: error.errors 
      });
    }

    res.status(500).json({ error: 'Failed to fetch messages' });
  }
};

// Send a message (for REST API fallback)
export const sendMessage = async (req: Request, res: Response) => {
  try {
    const messageData = sendMessageSchema.parse(req.body);
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is a member of the channel
    const channel = await prisma.channel.findFirst({
      where: {
        id: messageData.channelId,
        members: {
          some: { id: userId }
        }
      }
    });

    if (!channel) {
      return res.status(403).json({ error: 'Access denied to this channel' });
    }

    // Create the message
    const message = await prisma.message.create({
      data: {
        ...messageData,
        authorId: userId
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            avatarUrl: true,
            status: true
          }
        }
      }
    });

    res.status(201).json({ message });

  } catch (error) {
    console.error('Error sending message:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Validation error',
        details: error.errors 
      });
    }

    res.status(500).json({ error: 'Failed to send message' });
  }
};

// Delete a message
export const deleteMessage = async (req: Request, res: Response) => {
  try {
    const messageId = req.params.messageId;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Find the message and check ownership
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      include: {
        channel: {
          include: {
            members: true
          }
        }
      }
    });

    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }

    // Check if user owns the message or is channel owner
    const isOwner = message.authorId === userId;
    const isChannelOwner = message.channel.ownerId === userId;

    if (!isOwner && !isChannelOwner) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Delete the message
    await prisma.message.delete({
      where: { id: messageId }
    });

    res.json({ message: 'Message deleted successfully' });

  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({ error: 'Failed to delete message' });
  }
};

export { sendMessageSchema };

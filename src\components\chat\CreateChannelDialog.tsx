"use client";
import React, { useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { createChannel, clearError } from '@/store/slices/channelSlice';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { X, Loader2 } from 'lucide-react';

interface CreateChannelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CreateChannelDialog: React.FC<CreateChannelDialogProps> = ({ open, onOpenChange }) => {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.channels);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPrivate: false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) return;

    try {
      await dispatch(createChannel({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        isPrivate: formData.isPrivate,
      })).unwrap();
      
      // Reset form and close dialog
      setFormData({ name: '', description: '', isPrivate: false });
      onOpenChange(false);
    } catch (error) {
      // Error is handled by Redux
    }
  };

  const handleClose = () => {
    setFormData({ name: '', description: '', isPrivate: false });
    dispatch(clearError());
    onOpenChange(false);
  };

  const handleChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md bg-white" showCloseButton={false}>
        <DialogHeader>
          <DialogTitle>
            Create New Channel
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="channel-name">Channel Name *</Label>
            <Input
              id="channel-name"
              type="text"
              placeholder="Enter channel name"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              maxLength={50}
              disabled={isLoading}
              required
            />
            <p className="text-xs text-gray-500">
              {formData.name.length}/50 characters
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="channel-description">Description</Label>
            <Input
              id="channel-description"
              type="text"
              placeholder="Enter channel description (optional)"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              maxLength={200}
              disabled={isLoading}
            />
            <p className="text-xs text-gray-500">
              {formData.description.length}/200 characters
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="channel-private"
              checked={formData.isPrivate}
              onChange={(e) => handleChange('isPrivate', e.target.checked)}
              disabled={isLoading}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <Label htmlFor="channel-private" className="text-sm">
              Private Channel
            </Label>
          </div>
          <p className="text-xs text-gray-500">
            Private channels require an invitation to join
          </p>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !formData.name.trim()}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Channel'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateChannelDialog;

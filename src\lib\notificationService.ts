class NotificationService {
  private permission: NotificationPermission = 'default';
  private isClient: boolean = false;

  constructor() {
    this.isClient = typeof window !== 'undefined';
    if (this.isClient) {
      this.initializePermission();
    }
  }

  private async initializePermission() {
    if (this.isClient && 'Notification' in window) {
      this.permission = Notification.permission;
      
      if (this.permission === 'default') {
        this.permission = await Notification.requestPermission();
      }
    }
  }

  async requestPermission(): Promise<NotificationPermission> {
    if (!this.isClient || !('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return 'denied';
    }

    if (this.permission === 'default') {
      this.permission = await Notification.requestPermission();
    }

    return this.permission;
  }

  showNotification(title: string, options?: NotificationOptions): Notification | null {
    if (!this.isClient || !('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return null;
    }

    if (this.permission !== 'granted') {
      console.warn('Notification permission not granted');
      return null;
    }

    // Don't show notifications if the page is focused
    if (!document.hidden) {
      return null;
    }

    const notification = new Notification(title, {
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      ...options
    });

    // Auto-close after 5 seconds
    setTimeout(() => {
      notification.close();
    }, 5000);

    return notification;
  }

  showDirectMessageNotification(senderName: string, message: string, senderId: string) {
    return this.showNotification(`New message from ${senderName}`, {
      body: message,
      tag: `dm-${senderId}`, // Replace previous notifications from same sender
      icon: '/favicon.ico',
      data: {
        type: 'direct-message',
        senderId,
        senderName
      }
    });
  }

  showFriendRequestNotification(requesterName: string, requestId: string) {
    return this.showNotification(`Friend request from ${requesterName}`, {
      body: `${requesterName} wants to be your friend`,
      tag: `friend-request-${requestId}`,
      icon: '/favicon.ico',
      actions: [
        { action: 'accept', title: 'Accept' },
        { action: 'decline', title: 'Decline' }
      ],
      data: {
        type: 'friend-request',
        requestId,
        requesterName
      }
    });
  }

  showFriendRequestAcceptedNotification(friendName: string) {
    return this.showNotification(`Friend request accepted`, {
      body: `${friendName} accepted your friend request`,
      tag: `friend-accepted-${friendName}`,
      icon: '/favicon.ico',
      data: {
        type: 'friend-accepted',
        friendName
      }
    });
  }

  isSupported(): boolean {
    return this.isClient && 'Notification' in window;
  }

  isPermissionGranted(): boolean {
    return this.permission === 'granted';
  }

  getPermission(): NotificationPermission {
    return this.permission;
  }
}

// Export the class for dynamic instantiation
export { NotificationService };

// Singleton instance - only create on client side
const createNotificationService = (): NotificationService | null => {
  if (typeof window === 'undefined') {
    return null;
  }
  return new NotificationService();
};

const notificationService = createNotificationService();
export default notificationService;

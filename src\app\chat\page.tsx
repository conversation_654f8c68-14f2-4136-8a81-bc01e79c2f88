"use client";
import React, { useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { useRouter } from 'next/navigation';
import socketService from '@/lib/socket';
import { setConnected, resetMessages } from '@/store/slices/messageSlice';
import ChatLayout from '@/components/chat/ChatLayout';

const ChatPage: React.FC = () => {
  const { user, token } = useAppSelector((state) => state.auth);
  const { isConnected } = useAppSelector((state) => state.messages);
  const dispatch = useAppDispatch();
  const router = useRouter();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!user || !token) {
      router.push('/auth/login');
      return;
    }

    // Connect to Socket.IO server
    const connectSocket = () => {
      try {
        socketService.connect(token);
        
        // Set up socket event listeners
        const socket = socketService.getSocket();
        if (socket) {
          socket.on('connect', () => {
            console.log('Socket connected successfully');
            dispatch(setConnected(true));
          });

          socket.on('disconnect', () => {
            console.log('Socket disconnected');
            dispatch(setConnected(false));
          });

          socket.on('error', (error: any) => {
            console.error('Socket connection error:', error);
            dispatch(setConnected(false));
          });
        }
      } catch (error) {
        console.error('Failed to connect to socket:', error);
      }
    };

    connectSocket();

    // Handle browser close/refresh to emit logout event
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Emit logout event to server before page closes
      const socket = socketService.getSocket();
      if (socket) {
        socket.emit('logout');
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup on unmount
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      socketService.logout(); // Use proper logout method
      dispatch(setConnected(false));
      dispatch(resetMessages());
    };
  }, [user, token, dispatch, router]);

  // Show loading state while connecting
  if (!user || !token) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent text-blue-600 rounded-full mb-4" role="status" aria-label="loading">
            <span className="sr-only">Loading...</span>
          </div>
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-50">
      <ChatLayout />
      
      {/* Connection Status Indicator */}
      <div className="fixed top-4 right-4 z-50">
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          isConnected 
            ? 'bg-green-100 text-green-800 border border-green-200' 
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPage;

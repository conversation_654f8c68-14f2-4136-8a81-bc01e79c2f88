import { Response } from 'express';
import prisma from '../utils/database';
import { AuthenticatedRequest } from '../middleware/auth';

// Send invitation to a user
export const sendInvitation = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { channelId, inviteeId } = req.params;
    const inviterId = req.user.id;

    // Check if inviter is part of the channel
    const channel = await prisma.channel.findFirst({
      where: {
        id: channelId,
        members: {
          some: { id: inviterId }
        }
      },
      include: {
        members: true
      }
    });

    if (!channel) {
      return res.status(403).json({ error: 'Access denied to channel' });
    }

    // Check if invitee is already a member
    const isAlreadyMember = channel.members.some(member => member.id === inviteeId);

    if (isAlreadyMember) {
      return res.status(400).json({ error: 'User is already a member of the channel' });
    }

    // Create invitation
    const invitation = await prisma.invitation.create({
      data: {
        channelId,
        invitedById: inviterId,
        inviteeId
      }
    });

    res.status(201).json({ message: 'Invitation sent successfully', invitation });
  } catch (error) {
    console.error('Error sending invitation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Accept channel invitation
export const acceptInvitation = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { invitationId } = req.params;
    const userId = req.user.id;

    // Find invitation
    const invitation = await prisma.invitation.findUnique({
      where: { id: invitationId },
      include: {
        channel: true
      }
    });

    if (!invitation || invitation.inviteeId !== userId) {
      return res.status(403).json({ error: 'Invalid invitation' });
    }

    // Update invitation status
    await prisma.invitation.update({
      where: { id: invitationId },
      data: { status: 'ACCEPTED' }
    });

    // Add user to channel members
    await prisma.channel.update({
      where: { id: invitation.channelId },
      data: {
        members: {
          connect: { id: userId }
        }
      }
    });

    res.status(200).json({ message: 'Invitation accepted, added to channel' });
  } catch (error) {
    console.error('Error accepting invitation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Decline channel invitation
export const declineInvitation = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { invitationId } = req.params;
    const userId = req.user.id;

    // Find invitation
    const invitation = await prisma.invitation.findUnique({
      where: { id: invitationId },
    });

    if (!invitation || invitation.inviteeId !== userId) {
      return res.status(403).json({ error: 'Invalid invitation' });
    }

    // Update invitation status
    await prisma.invitation.update({
      where: { id: invitationId },
      data: { status: 'DECLINED' }
    });

    res.status(200).json({ message: 'Invitation declined' });
  } catch (error) {
    console.error('Error declining invitation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get received invitations
export const getReceivedInvitations = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user.id;

    const invitations = await prisma.invitation.findMany({
      where: {
        inviteeId: userId,
        status: 'PENDING'
      },
      include: {
        channel: {
          select: {
            id: true,
            name: true
          }
        },
        invitedBy: {
          select: {
            id: true,
            username: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({ invitations });
  } catch (error) {
    console.error('Error retrieving received invitations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get sent invitations
export const getSentInvitations = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const inviterId = req.user.id;

    const invitations = await prisma.invitation.findMany({
      where: {
        invitedById: inviterId
      },
      include: {
        channel: {
          select: {
            id: true,
            name: true
          }
        },
        invitee: {
          select: {
            id: true,
            username: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({ invitations });
  } catch (error) {
    console.error('Error retrieving sent invitations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};


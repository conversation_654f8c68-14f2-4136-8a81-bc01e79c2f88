import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface User {
  id: string;
  username: string;
  avatarUrl?: string;
  status: 'ONLINE' | 'AWAY' | 'BUSY' | 'OFFLINE';
}

export interface Message {
  id: string;
  content: string;
  messageType: 'TEXT' | 'FILE' | 'IMAGE' | 'SYSTEM';
  channelId: string;
  authorId: string;
  author: User;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  fileMimeType?: string;
  isEdited: boolean;
  editedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Channel {
  id: string;
  name: string;
  description?: string;
  isPrivate: boolean;
  ownerId: string;
  members: User[];
  createdAt: string;
  updatedAt: string;
}

export interface TypingUser {
  userId: string;
  username: string;
  channelId: string;
}

interface MessageState {
  messages: Record<string, Message[]>; // channelId -> messages[]
  channels: Channel[];
  currentChannel: Channel | null;
  typingUsers: TypingUser[];
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  hasMoreMessages: Record<string, boolean>; // channelId -> hasMore
}

const initialState: MessageState = {
  messages: {},
  channels: [],
  currentChannel: null,
  typingUsers: [],
  isConnected: false,
  isLoading: false,
  error: null,
  hasMoreMessages: {}
};

// Async thunks for API calls
export const fetchMessages = createAsyncThunk(
  'messages/fetchMessages',
  async ({ channelId, page = 1 }: { channelId: string; page?: number }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/messages/${channelId}?page=${page}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch messages');
      }

      const data = await response.json();
      return { channelId, ...data };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const messageSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {
    // Socket connection status
    setConnected: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },

    // Channel management
    setCurrentChannel: (state, action: PayloadAction<Channel | null>) => {
      state.currentChannel = action.payload;
    },

    addChannel: (state, action: PayloadAction<Channel>) => {
      const existingIndex = state.channels.findIndex(ch => ch.id === action.payload.id);
      if (existingIndex >= 0) {
        state.channels[existingIndex] = action.payload;
      } else {
        state.channels.push(action.payload);
      }
    },

    updateChannelMembers: (state, action: PayloadAction<{ channelId: string; members: User[] }>) => {
      const channel = state.channels.find(ch => ch.id === action.payload.channelId);
      if (channel) {
        channel.members = action.payload.members;
      }
      if (state.currentChannel?.id === action.payload.channelId) {
        state.currentChannel.members = action.payload.members;
      }
    },

    // Message management
    addMessage: (state, action: PayloadAction<Message>) => {
      const { channelId } = action.payload;
      if (!state.messages[channelId]) {
        state.messages[channelId] = [];
      }
      
      // Check if message already exists (prevent duplicates)
      const existingMessage = state.messages[channelId].find(msg => msg.id === action.payload.id);
      if (!existingMessage) {
        state.messages[channelId].push(action.payload);
        // Sort messages by creation time
        state.messages[channelId].sort((a, b) => 
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      }
    },

    setMessages: (state, action: PayloadAction<{ channelId: string; messages: Message[] }>) => {
      state.messages[action.payload.channelId] = action.payload.messages;
    },

    prependMessages: (state, action: PayloadAction<{ channelId: string; messages: Message[] }>) => {
      const { channelId, messages } = action.payload;
      if (!state.messages[channelId]) {
        state.messages[channelId] = [];
      }
      state.messages[channelId] = [...messages, ...state.messages[channelId]];
    },

    removeMessage: (state, action: PayloadAction<{ channelId: string; messageId: string }>) => {
      const { channelId, messageId } = action.payload;
      if (state.messages[channelId]) {
        state.messages[channelId] = state.messages[channelId].filter(msg => msg.id !== messageId);
      }
    },

    updateMessage: (state, action: PayloadAction<Message>) => {
      const { channelId } = action.payload;
      if (state.messages[channelId]) {
        const index = state.messages[channelId].findIndex(msg => msg.id === action.payload.id);
        if (index >= 0) {
          state.messages[channelId][index] = action.payload;
        }
      }
    },

    // Typing indicators
    setUserTyping: (state, action: PayloadAction<{ userId: string; username: string; channelId: string; isTyping: boolean }>) => {
      const { userId, username, channelId, isTyping } = action.payload;
      
      if (isTyping) {
        // Add typing user if not already present
        const existingIndex = state.typingUsers.findIndex(u => u.userId === userId && u.channelId === channelId);
        if (existingIndex === -1) {
          state.typingUsers.push({ userId, username, channelId });
        }
      } else {
        // Remove typing user
        state.typingUsers = state.typingUsers.filter(u => !(u.userId === userId && u.channelId === channelId));
      }
    },

    clearTypingUsers: (state, action: PayloadAction<string>) => {
      const channelId = action.payload;
      state.typingUsers = state.typingUsers.filter(u => u.channelId !== channelId);
    },

    // Pagination
    setHasMoreMessages: (state, action: PayloadAction<{ channelId: string; hasMore: boolean }>) => {
      state.hasMoreMessages[action.payload.channelId] = action.payload.hasMore;
    },

    // Error handling
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    clearError: (state) => {
      state.error = null;
    },

    // Reset state
    resetMessages: (state) => {
      state.messages = {};
      state.currentChannel = null;
      state.typingUsers = [];
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMessages.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.isLoading = false;
        const { channelId, messages, pagination } = action.payload;
        
        if (pagination.currentPage === 1) {
          // First page - replace messages
          state.messages[channelId] = messages;
        } else {
          // Additional pages - prepend messages
          if (!state.messages[channelId]) {
            state.messages[channelId] = [];
          }
          state.messages[channelId] = [...messages, ...state.messages[channelId]];
        }
        
        state.hasMoreMessages[channelId] = pagination.hasMore;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }
});

export const {
  setConnected,
  setCurrentChannel,
  addChannel,
  updateChannelMembers,
  addMessage,
  setMessages,
  prependMessages,
  removeMessage,
  updateMessage,
  setUserTyping,
  clearTypingUsers,
  setHasMoreMessages,
  setError,
  clearError,
  resetMessages
} = messageSlice.actions;

export default messageSlice.reducer;

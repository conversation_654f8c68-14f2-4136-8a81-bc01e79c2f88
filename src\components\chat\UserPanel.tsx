"use client";
import React from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { logoutUser } from '@/store/slices/authSlice';
import { resetMessages } from '@/store/slices/messageSlice';
import socketService from '@/lib/socket';

interface UserPanelProps {
  collapsed: boolean;
}

const UserPanel: React.FC<UserPanelProps> = ({ collapsed }) => {
  const { user } = useAppSelector((state) => state.auth);
  const { isConnected } = useAppSelector((state) => state.messages);
  const dispatch = useAppDispatch();

  const handleLogout = async () => {
    // First emit logout event via socket to notify server
    socketService.logout();
    
    // Reset messages state
    dispatch(resetMessages());
    
    // Call backend logout endpoint and clear local state
    dispatch(logoutUser());
  };

  if (!user) return null;

  return (
    <div className="p-4">
      <div className="flex items-center space-x-3">
        {/* User Avatar */}
        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
          {user.username.charAt(0).toUpperCase()}
        </div>

        {!collapsed && (
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div className="min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user.username}
                </p>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-green-400' : 'bg-red-400'
                  }`}></div>
                  <p className="text-xs text-gray-400">
                    {isConnected ? 'Online' : 'Offline'}
                  </p>
                </div>
              </div>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="p-1 hover:bg-gray-700 rounded text-gray-400 hover:text-white transition-colors"
                title="Logout"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {collapsed && (
          <button
            onClick={handleLogout}
            className="p-1 hover:bg-gray-700 rounded text-gray-400 hover:text-white transition-colors"
            title="Logout"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

export default UserPanel;

"use client";
import React, { useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchMessages, setCurrentChannel } from '@/store/slices/messageSlice';
import { 
  fetchUserChannels, 
  fetchPublicChannels, 
  joinChannel, 
  leaveChannel,
  setSelectedChannel 
} from '@/store/slices/channelSlice';
import socketService from '@/lib/socket';
import CreateChannelDialog from './CreateChannelDialog';
import AddMembersDialog from './AddMembersDialog';
import { Button } from '@/components/ui/button';
import { Plus, Hash, Lock, Users, MoreVertical, LogOut } from 'lucide-react';

interface ChannelSidebarProps {
  collapsed: boolean;
}

const ChannelSidebar: React.FC<ChannelSidebarProps> = ({ collapsed }) => {
  const { channels } = useAppSelector((state) => state.messages);
  const { userChannels, publicChannels, selectedChannel, isLoading } = useAppSelector((state) => state.channels);
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showPublicChannels, setShowPublicChannels] = useState(false);
  const [showAddMembersDialog, setShowAddMembersDialog] = useState(false);

  useEffect(() => {
    if (user) {
      dispatch(fetchUserChannels());
      if (showPublicChannels) {
        dispatch(fetchPublicChannels());
      }
    }
  }, [dispatch, user, showPublicChannels]);

  const handleChannelSelect = (channelId: string) => {
    // Find channel in either user channels or message channels
    const selectedChannelData = userChannels.find((channel) => channel.id === channelId) ||
                               channels.find((channel) => channel.id === channelId);
    
    if (selectedChannelData) {
      dispatch(setSelectedChannel(selectedChannelData));
      dispatch(setCurrentChannel(selectedChannelData));
      socketService.joinChannel(channelId);
    }
  };

  const handleJoinChannel = async (channelId: string) => {
    try {
      await dispatch(joinChannel(channelId)).unwrap();
      // Refresh user channels after joining
      dispatch(fetchUserChannels());
    } catch (error) {
      console.error('Failed to join channel:', error);
    }
  };

  const handleLeaveChannel = async (channelId: string) => {
    try {
      await dispatch(leaveChannel(channelId)).unwrap();
      // Refresh user channels after leaving
      dispatch(fetchUserChannels());
      // If this was the selected channel, clear selection
      if (selectedChannel?.id === channelId) {
        dispatch(setSelectedChannel(null));
      }
    } catch (error) {
      console.error('Failed to leave channel:', error);
    }
  };

  const isUserInChannel = (channelId: string) => {
    return userChannels.some(channel => channel.id === channelId);
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-200">
            {collapsed ? '🔥' : 'Channels'}
          </h2>
          {!collapsed && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowCreateDialog(true)}
              className="h-6 w-6 p-0 text-gray-400 hover:text-white"
            >
              <Plus className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* User Channels */}
        <div className="space-y-2 mb-6">
          {!collapsed && (
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                Your Channels
              </h3>
              {selectedChannel && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowAddMembersDialog(true)}
                  className="h-6 w-6 p-0 text-gray-400 hover:text-white"
                >
                  <Users className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}
          {userChannels.map((channel) => (
            <div key={channel.id} className="flex items-center group">
              <button
                onClick={() => handleChannelSelect(channel.id)}
                className={`flex-1 flex items-center text-left p-2 text-sm rounded transition-colors ${
                  selectedChannel?.id === channel.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                {collapsed ? (
                  <span className="block w-8 text-center font-semibold">
                    {channel.name.charAt(0).toUpperCase()}
                  </span>
                ) : (
                  <>
                    {channel.isPrivate ? (
                      <Lock className="h-4 w-4 mr-2 flex-shrink-0" />
                    ) : (
                      <Hash className="h-4 w-4 mr-2 flex-shrink-0" />
                    )}
                    <span className="truncate">{channel.name}</span>
                    {channel.memberCount && (
                      <span className="ml-auto text-xs text-gray-400">
                        {channel.memberCount}
                      </span>
                    )}
                  </>
                )}
              </button>
              {!collapsed && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleLeaveChannel(channel.id)}
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-400 ml-1"
                >
                  <LogOut className="h-3 w-3" />
                </Button>
              )}
            </div>
          ))}
        </div>

        {/* Public Channels */}
        {!collapsed && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                Browse Channels
              </h3>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowPublicChannels(!showPublicChannels)}
                className="h-6 text-xs text-gray-400 hover:text-white"
              >
                {showPublicChannels ? 'Hide' : 'Show'}
              </Button>
            </div>
            
            {showPublicChannels && (
              <div className="space-y-1">
                {publicChannels
                  .filter(channel => !isUserInChannel(channel.id))
                  .map((channel) => (
                    <div key={channel.id} className="flex items-center group">
                      <div className="flex-1 flex items-center text-left p-2 text-sm text-gray-400">
                        {channel.isPrivate ? (
                          <Lock className="h-4 w-4 mr-2 flex-shrink-0" />
                        ) : (
                          <Hash className="h-4 w-4 mr-2 flex-shrink-0" />
                        )}
                        <span className="truncate">{channel.name}</span>
                        {channel.description && (
                          <span className="ml-2 text-xs text-gray-500 truncate">
                            - {channel.description}
                          </span>
                        )}
                        {channel.memberCount && (
                          <span className="ml-auto text-xs text-gray-500">
                            {channel.memberCount}
                          </span>
                        )}
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleJoinChannel(channel.id)}
                        disabled={isLoading}
                        className="h-6 px-2 text-xs opacity-0 group-hover:opacity-100 text-green-400 hover:text-green-300"
                      >
                        Join
                      </Button>
                    </div>
                  ))
                }
                {publicChannels.filter(channel => !isUserInChannel(channel.id)).length === 0 && (
                  <p className="text-xs text-gray-500 p-2">
                    No public channels to join
                  </p>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Create Channel Dialog */}
      <CreateChannelDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />

      {/* Add Members Dialog */}
      <AddMembersDialog
        open={showAddMembersDialog}
        onOpenChange={setShowAddMembersDialog}
        channelId={selectedChannel?.id || ''}
        channelName={selectedChannel?.name || ''}
      />
    </div>
  );
};

export default ChannelSidebar;


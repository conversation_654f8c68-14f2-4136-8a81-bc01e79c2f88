import { Response } from 'express';
import prisma from '../utils/database';
import { AuthenticatedRequest } from '../middleware/auth';
import { 
  emitChannelCreated, 
  emitChannelUpdated, 
  emitChannelMembershipChanged, 
  emitChannelDeleted 
} from '../utils/socket';

// Get all channels user is a member of
export const getUserChannels = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user.id;

    const channels = await prisma.channel.findMany({
      where: {
        members: {
          some: { id: userId }
        }
      },
      include: {
        owner: {
          select: {
            id: true,
            username: true,
            email: true
          }
        },
        members: {
          select: {
            id: true,
            username: true,
            email: true,
            status: true,
            isOnline: true
          }
        },
        _count: {
          select: {
            messages: true,
            members: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    res.status(200).json({ channels });
  } catch (error) {
    console.error('Error getting user channels:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Get public channels
export const getPublicChannels = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const channels = await prisma.channel.findMany({
      where: {
        isPrivate: false
      },
      include: {
        owner: {
          select: {
            id: true,
            username: true,
            email: true
          }
        },
        _count: {
          select: {
            members: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({ channels });
  } catch (error) {
    console.error('Error getting public channels:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Create a new channel
export const createChannel = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { name, description, isPrivate } = req.body;
    const userId = req.user.id;

    // Check if channel name already exists
    const existingChannel = await prisma.channel.findFirst({
      where: { name }
    });

    if (existingChannel) {
      return res.status(400).json({ error: 'Channel name already exists' });
    }

    // Create channel and add owner as member
    const channel = await prisma.channel.create({
      data: {
        name,
        description,
        isPrivate: isPrivate || false,
        owner: { connect: { id: userId } },
        members: { connect: { id: userId } }
      },
      include: {
        owner: {
          select: {
            id: true,
            username: true,
            email: true
          }
        },
        members: {
          select: {
            id: true,
            username: true,
            email: true
          }
        },
        _count: {
          select: { members: true }
        }
      }
    });

    // Emit WebSocket event for channel creation
    try {
      emitChannelCreated(channel, { 
        userId: req.user.id, 
        username: req.user.username 
      });
    } catch (error) {
      console.error('Error emitting channel created event:', error);
    }

    res.status(201).json({ message: 'Channel created successfully', channel });
  } catch (error) {
    console.error('Error creating channel:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Join a channel
export const joinChannel = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { channelId } = req.params;
    const userId = req.user.id;

    // Check if channel exists
    const channel = await prisma.channel.findUnique({
      where: { id: channelId },
      include: {
        members: {
          select: { id: true }
        }
      }
    });

    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    // Check if user is already a member
    const isAlreadyMember = channel.members.some(member => member.id === userId);
    if (isAlreadyMember) {
      return res.status(400).json({ error: 'You are already a member of this channel' });
    }

    // Add user to channel members
    await prisma.channel.update({
      where: { id: channelId },
      data: { members: { connect: { id: userId } } },
    });

    // Emit WebSocket event for membership change
    try {
      emitChannelMembershipChanged(
        channelId, 
        req.user.id, 
        req.user.username, 
        'joined'
      );
    } catch (error) {
      console.error('Error emitting channel membership event:', error);
    }

    res.status(200).json({ message: 'Joined channel successfully' });
  } catch (error) {
    console.error('Error joining channel:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Update channel settings
export const updateChannel = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { channelId } = req.params;
    const { name, description, isPrivate } = req.body;
    const userId = req.user.id;

    // Check if channel exists and user is owner
    const channel = await prisma.channel.findUnique({
      where: { id: channelId },
      select: { ownerId: true }
    });

    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    if (channel.ownerId !== userId) {
      return res.status(403).json({ error: 'Only channel owner can update settings' });
    }

    // Check if new name already exists (if name is being changed)
    if (name) {
      const existingChannel = await prisma.channel.findFirst({
        where: { 
          name,
          id: { not: channelId }
        }
      });

      if (existingChannel) {
        return res.status(400).json({ error: 'Channel name already exists' });
      }
    }

    // Update channel details
    const updatedChannel = await prisma.channel.update({
      where: { id: channelId },
      data: { name, description, isPrivate },
      include: {
        owner: {
          select: {
            id: true,
            username: true,
            email: true
          }
        },
        members: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    // Emit WebSocket event for channel update
    try {
      emitChannelUpdated(channelId, { 
        userId: req.user.id, 
        username: req.user.username 
      });
    } catch (error) {
      console.error('Error emitting channel updated event:', error);
    }

    res.status(200).json({ message: 'Channel updated successfully', channel: updatedChannel });
  } catch (error) {
    console.error('Error updating channel:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Leave a channel
export const leaveChannel = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { channelId } = req.params;
    const userId = req.user.id;

    // Check if channel exists and user is a member
    const channel = await prisma.channel.findUnique({
      where: { id: channelId },
      include: {
        members: {
          select: { id: true }
        }
      },
      select: {
        id: true,
        ownerId: true,
        members: {
          select: { id: true }
        }
      }
    });

    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    // Check if user is a member
    const isMember = channel.members.some(member => member.id === userId);
    if (!isMember) {
      return res.status(400).json({ error: 'You are not a member of this channel' });
    }

    // Prevent owner from leaving their own channel
    if (channel.ownerId === userId) {
      return res.status(400).json({ error: 'Channel owner cannot leave. Delete the channel instead.' });
    }

    // Remove user from channel members
    await prisma.channel.update({
      where: { id: channelId },
      data: { members: { disconnect: { id: userId } } },
    });

    // Emit WebSocket event for membership change
    try {
      emitChannelMembershipChanged(
        channelId, 
        req.user.id, 
        req.user.username, 
        'left'
      );
    } catch (error) {
      console.error('Error emitting channel membership event:', error);
    }

    res.status(200).json({ message: 'Left channel successfully' });
  } catch (error) {
    console.error('Error leaving channel:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Delete a channel
export const deleteChannel = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { channelId } = req.params;
    const userId = req.user.id;

    // Check if channel exists and user is owner
    const channel = await prisma.channel.findUnique({
      where: { id: channelId },
      select: { ownerId: true, name: true }
    });

    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    if (channel.ownerId !== userId) {
      return res.status(403).json({ error: 'Only channel owner can delete the channel' });
    }

    // Delete channel (messages will be cascade deleted)
    await prisma.channel.delete({
      where: { id: channelId },
    });

    // Emit WebSocket event for channel deletion
    try {
      emitChannelDeleted(channelId, channel.name, { 
        userId: req.user.id, 
        username: req.user.username 
      });
    } catch (error) {
      console.error('Error emitting channel deleted event:', error);
    }

    res.status(200).json({ message: 'Channel deleted successfully' });
  } catch (error) {
    console.error('Error deleting channel:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};


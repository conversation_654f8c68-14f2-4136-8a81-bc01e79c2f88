import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Create sample users
  const hashedPassword1 = await bcrypt.hash('password123', 10);
  const hashedPassword2 = await bcrypt.hash('password123', 10);

  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'alice',
      password: hashedPassword1,
    },
  });

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'bob',
      password: hashedPassword2,
    },
  });

  // Create a sample channel (delete existing first if any)
  await prisma.channel.deleteMany({
    where: { name: 'general' }
  });
  
  const channel = await prisma.channel.create({
    data: {
      name: 'general',
      description: 'General discussion channel',
      ownerId: user1.id,
      members: {
        connect: [{ id: user1.id }, { id: user2.id }],
      },
    },
  });

  console.log('Seed data created:');
  console.log('User 1:', { email: user1.email, username: user1.username });
  console.log('User 2:', { email: user2.email, username: user2.username });
  console.log('Channel:', { name: channel.name, id: channel.id });
  console.log('\nYou can login with:');
  console.log('Email: alice@example.<NAME_EMAIL>');
  console.log('Password: password123');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

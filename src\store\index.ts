import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import messageSlice from './slices/messageSlice';
import channelSlice from './slices/channelSlice';
import friendsSlice from './slices/friendsSlice';
import directMessagesSlice from './slices/directMessagesSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    messages: messageSlice,
    channels: channelSlice,
    friends: friendsSlice,
    directMessages: directMessagesSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

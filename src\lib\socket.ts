import { io, Socket } from 'socket.io-client';
import notificationService from './notificationService';
import { store } from '@/store';

class SocketService {
  private socket: Socket | null = null;
  private token: string | null = null;

  connect(token: string) {
    if (this.socket?.connected) {
      return this.socket;
    }

    this.token = token;
    this.socket = io(process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:5000', {
      auth: {
        token: token
      },
      transports: ['polling', 'websocket'], // Try polling first, then websocket
      reconnection: true,
      reconnectionAttempts: 10,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 20000,
      forceNew: true // Force a new connection
    });

    // Connection event handlers
    this.socket.on('connect', () => {
      console.log('Connected to server:', this.socket?.id);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from server:', reason);
      if (reason === 'io server disconnect') {
        // The disconnection was initiated by the server, reconnect manually
        this.socket?.connect();
      }
    });

    this.socket.on('reconnect', (attempt) => {
      console.log('Reconnected to server after', attempt, 'attempts');
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('Reconnection failed:', error);
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  logout() {
    if (this.socket) {
      // Emit logout event to let server know this is an intentional logout
      this.socket.emit('logout');
      // Then disconnect
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Channel operations
  joinChannel(channelId: string) {
    if (this.socket) {
      this.socket.emit('joinChannel', { channelId });
    }
  }

  leaveChannel(channelId: string) {
    if (this.socket) {
      this.socket.emit('leaveChannel', { channelId });
    }
  }

  // Message operations
  sendMessage(messageData: {
    content: string;
    channelId: string;
    messageType?: string;
  }) {
    if (this.socket) {
      this.socket.emit('sendMessage', messageData);
    }
  }

  // Typing indicators
  startTyping(channelId: string, username: string) {
    if (this.socket) {
      this.socket.emit('typing', { channelId, username });
    }
  }

  stopTyping(channelId: string, username: string) {
    if (this.socket) {
      this.socket.emit('stopTyping', { channelId, username });
    }
  }

  // Channel events
  onNewPublicChannel(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('newPublicChannel', callback);
    }
  }

  onChannelUpdate(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('channelUpdated', callback);
    }
  }

  onMembershipChange(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('channelMembershipChanged', callback);
    }
  }

  onChannelDeletion(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('channelDeleted', callback);
    }
  }

  // Event listeners
  onMessageReceived(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('messageReceived', callback);
    }
  }

  onUserJoined(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('userJoined', callback);
    }
  }

  onUserLeft(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('userLeft', callback);
    }
  }

  onUserTyping(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('userTyping', callback);
    }
  }

  onChannelJoined(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('channelJoined', callback);
    }
  }

  onUserOffline(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('userOffline', callback);
    }
  }

  // Direct message operations
  sendDirectMessage(messageData: {
    receiverId: string;
    content: string;
    messageType?: string;
  }) {
    if (this.socket) {
      this.socket.emit('sendDirectMessage', messageData);
    }
  }

  onDirectMessageReceived(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('directMessageReceived', (data) => {
        // Show notification for direct messages
        const state = store.getState();
        const currentUser = state.auth.user;
        
        if (notificationService // Check if notificationService is initialized
            && data.senderId !== currentUser?.id) {
          // Try to decrypt message content for notification
          let displayContent = data.content;
          try {
            const parsed = JSON.parse(data.content);
            if (parsed.encrypted) {
              displayContent = '[Encrypted message]';
            }
          } catch {
            // Not encrypted, use as is
          }
          
          notificationService.showDirectMessageNotification(
            data.sender?.username || 'Unknown User',
            displayContent,
            data.senderId
          );
        }
        
        callback(data);
      });
    }
  }

  onDirectMessageSent(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('directMessageSent', callback);
    }
  }

  // Friend request operations
  sendFriendRequest(addresseeId: string) {
    if (this.socket) {
      this.socket.emit('sendFriendRequest', { addresseeId });
    }
  }

  manageFriendRequest(friendshipId: string, action: 'accept' | 'decline') {
    if (this.socket) {
      this.socket.emit('manageFriendRequest', { friendshipId, action });
    }
  }

  onFriendRequestReceived(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('friendRequestReceived', (data) => {
        // Show notification for friend requests
        if (notificationService) { // Check if notificationService is initialized
          notificationService.showFriendRequestNotification(
            data.requester?.username || 'Unknown User',
            data.id
          );
        }
        
        callback(data);
      });
    }
  }

  onFriendRequestUpdated(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('friendRequestUpdated', (data) => {
        // Show notification when friend request is accepted
        if (notificationService // Check if notificationService is initialized
            && data.status === 'ACCEPTED') {
          notificationService.showFriendRequestAcceptedNotification(
            data.addressee?.username || 'Unknown User'
          );
        }
        
        callback(data);
      });
    }
  }

  // Remove listeners
  removeAllListeners() {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  removeListener(event: string) {
    if (this.socket) {
      this.socket.removeAllListeners(event);
    }
  }
}

// Create singleton instance
const socketService = new SocketService();
export default socketService;

import express from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  sendDirectMessage,
  getDirectMessages,
  getDirectMessageConversations,
  markDirectMessagesAsRead,
  getUnreadDirectMessageCount
} from '../controllers/directMessagesController';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Direct message routes
router.post('/', sendDirectMessage);
router.get('/conversations', getDirectMessageConversations);
router.get('/', getDirectMessages);
router.post('/mark-read', markDirectMessagesAsRead);
router.get('/unread-count', getUnreadDirectMessageCount);

export default router;

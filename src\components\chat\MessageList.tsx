"use client";
import React, { useEffect, useRef } from 'react';
import { Message } from '@/store/slices/messageSlice';
import { useAppSelector } from '@/store/hooks';

interface MessageListProps {
  messages: Message[];
}

interface MessageItemProps {
  message: Message;
  isOwn: boolean;
  showAvatar: boolean;
}

const MessageItem: React.FC<MessageItemProps> = ({ message, isOwn, showAvatar }) => {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className={`flex items-start space-x-3 px-6 py-2 hover:bg-gray-50 ${
      isOwn ? 'flex-row-reverse space-x-reverse' : ''
    }`}>
      {/* Avatar */}
      <div className="flex-shrink-0">
        {showAvatar ? (
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
            {message.author.username.charAt(0).toUpperCase()}
          </div>
        ) : (
          <div className="w-8 h-8"></div>
        )}
      </div>

      {/* Message Content */}
      <div className={`flex-1 min-w-0 ${isOwn ? 'text-right' : ''}`}>
        {showAvatar && (
          <div className={`flex items-center space-x-2 mb-1 ${isOwn ? 'justify-end' : ''}`}>
            <span className="text-sm font-medium text-gray-900">
              {message.author.username}
            </span>
            <span className="text-xs text-gray-500">
              {formatTime(message.createdAt)}
            </span>
            {message.isEdited && (
              <span className="text-xs text-gray-400">(edited)</span>
            )}
          </div>
        )}
        
        <div className={`${isOwn ? 'text-right' : ''}`}>
          {message.messageType === 'TEXT' && (
            <div className={`inline-block max-w-xs lg:max-w-md xl:max-w-lg px-4 py-2 rounded-lg ${
              isOwn 
                ? 'bg-blue-500 text-white ml-auto' 
                : 'bg-gray-100 text-gray-900'
            }`}>
              <p className="text-sm whitespace-pre-wrap break-words">
                {message.content}
              </p>
            </div>
          )}

          {message.messageType === 'FILE' && (
            <div className={`inline-block max-w-xs lg:max-w-md xl:max-w-lg px-4 py-3 rounded-lg border ${
              isOwn 
                ? 'bg-blue-500 text-white border-blue-600 ml-auto' 
                : 'bg-white text-gray-900 border-gray-200'
            }`}>
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded ${
                  isOwn ? 'bg-blue-400' : 'bg-gray-200'
                } flex items-center justify-center`}>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {message.fileName || 'File'}
                  </p>
                  {message.fileSize && (
                    <p className={`text-xs ${isOwn ? 'text-blue-100' : 'text-gray-500'}`}>
                      {(message.fileSize / 1024 / 1024).toFixed(2)} MB
                    </p>
                  )}
                </div>
              </div>
              {message.content && (
                <p className="text-sm mt-2 whitespace-pre-wrap break-words">
                  {message.content}
                </p>
              )}
            </div>
          )}

          {message.messageType === 'SYSTEM' && (
            <div className="text-center">
              <span className="inline-block px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                {message.content}
              </span>
            </div>
          )}
        </div>

        {!showAvatar && !isOwn && (
          <div className="text-xs text-gray-400 mt-1">
            {formatTime(message.createdAt)}
          </div>
        )}
      </div>
    </div>
  );
};

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  const { user } = useAppSelector((state) => state.auth);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <div className="text-4xl mb-2">💬</div>
          <p>No messages yet</p>
          <p className="text-sm">Be the first to send a message!</p>
        </div>
      </div>
    );
  }

  // Group messages by date and determine when to show avatar
  const processedMessages = messages.map((message, index) => {
    const prevMessage = index > 0 ? messages[index - 1] : null;
    const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
    
    const showAvatar = !prevMessage || 
      prevMessage.authorId !== message.authorId ||
      new Date(message.createdAt).getTime() - new Date(prevMessage.createdAt).getTime() > 300000; // 5 minutes

    const showDateSeparator = !prevMessage ||
      new Date(message.createdAt).toDateString() !== new Date(prevMessage.createdAt).toDateString();

    return {
      ...message,
      showAvatar,
      showDateSeparator,
      isOwn: message.authorId === user?.id
    };
  });

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="py-4">
        {processedMessages.map((message) => (
          <React.Fragment key={message.id}>
            {message.showDateSeparator && (
              <div className="flex items-center my-6 px-6">
                <div className="flex-1 border-t border-gray-200"></div>
                <div className="px-4 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                  {formatDate(message.createdAt)}
                </div>
                <div className="flex-1 border-t border-gray-200"></div>
              </div>
            )}
            <MessageItem
              message={message}
              isOwn={message.isOwn}
              showAvatar={message.showAvatar}
            />
          </React.Fragment>
        ))}
      </div>
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkMessages() {
  try {
    console.log('🔍 Checking all remaining messages...');
    
    const allMessages = await prisma.directMessage.findMany({
      select: {
        id: true,
        content: true,
        createdAt: true,
        sender: {
          select: {
            id: true,
            username: true
          }
        },
        receiver: {
          select: {
            id: true,
            username: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 Total messages in database: ${allMessages.length}`);
    
    if (allMessages.length === 0) {
      console.log('✅ No messages found - database is clean!');
      return;
    }
    
    allMessages.forEach((message, index) => {
      console.log(`\n📧 Message ${index + 1}:`);
      console.log(`   ID: ${message.id}`);
      console.log(`   From: ${message.sender.username} (${message.sender.id})`);
      console.log(`   To: ${message.receiver.username} (${message.receiver.id})`);
      console.log(`   Created: ${message.createdAt}`);
      console.log(`   Content: ${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}`);
      
      // Check if it's encrypted
      try {
        const parsed = JSON.parse(message.content);
        if (parsed.encrypted) {
          console.log(`   🔒 ENCRYPTED MESSAGE (should not exist!)`);
        } else {
          console.log(`   📝 Regular JSON content`);
        }
      } catch {
        console.log(`   📝 Plain text message`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error checking messages:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

checkMessages()
  .then(() => {
    console.log('\n✅ Message check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Check failed:', error);
    process.exit(1);
  });

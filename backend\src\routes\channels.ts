import { Router } from 'express';
import { 
  getUserChannels, 
  getPublicChannels,
  createChannel, 
  joinChannel, 
  updateChannel, 
  leaveChannel, 
  deleteChannel 
} from '../controllers/channelController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest, createChannelSchema, updateChannelSchema } from '../middleware/validation';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// Get user's channels
router.get('/my-channels', getUserChannels);

// Get public channels
router.get('/public', getPublicChannels);

// Create a new channel
router.post('/', validateRequest(createChannelSchema), createChannel);

// Join a channel
router.post('/:channelId/join', joinChannel);

// Update channel settings
router.put('/:channelId', validateRequest(updateChannelSchema), updateChannel);

// Leave a channel
router.post('/:channelId/leave', leaveChannel);

// Delete a channel
router.delete('/:channelId', deleteChannel);

export default router;

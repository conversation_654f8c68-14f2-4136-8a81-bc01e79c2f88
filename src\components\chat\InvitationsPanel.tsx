"use client";
import React, { useState, useEffect } from 'react';
import { useAppSelector } from '@/store/hooks';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Hash, Lock, Clock } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface Invitation {
  id: string;
  channel: {
    id: string;
    name: string;
    isPrivate: boolean;
    description?: string;
  };
  inviter: {
    id: string;
    username: string;
  };
  createdAt: string;
  status: 'PENDING' | 'ACCEPTED' | 'DECLINED';
}

interface InvitationsPanelProps {
  collapsed: boolean;
}

const InvitationsPanel: React.FC<InvitationsPanelProps> = ({ collapsed }) => {
  const { user } = useAppSelector((state) => state.auth);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (user) {
      fetchInvitations();
    }
  }, [user]);

  const fetchInvitations = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/invitations/received`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        // Ensure data is an array before filtering
        const invitationsArray = Array.isArray(data) ? data : [];
        setInvitations(invitationsArray.filter((inv: Invitation) => inv.status === 'PENDING'));
      } else {
        console.error('Failed to fetch invitations');
      }
    } catch (error) {
      console.error('Error fetching invitations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInvitationResponse = async (invitationId: string, action: 'accept' | 'decline') => {
    setProcessingIds(prev => new Set([...prev, invitationId]));

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/invitations/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invitationId }),
      });

      if (response.ok) {
        // Remove the invitation from the list
        setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
        
        if (action === 'accept') {
          // Optionally, you could trigger a refresh of user channels here
          // dispatch(fetchUserChannels());
        }
      } else {
        const error = await response.json();
        console.error(`Failed to ${action} invitation:`, error.message);
      }
    } catch (error) {
      console.error(`Error ${action}ing invitation:`, error);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(invitationId);
        return newSet;
      });
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getUserInitials = (username: string) => {
    return username.slice(0, 2).toUpperCase();
  };

  if (collapsed) {
    return (
      <div className="p-2">
        {invitations.length > 0 && (
          <div className="flex justify-center">
            <Badge variant="secondary" className="text-xs">
              {invitations.length}
            </Badge>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-200">
          Channel Invitations
        </h3>
        {invitations.length > 0 && (
          <Badge variant="secondary" className="text-xs">
            {invitations.length}
          </Badge>
        )}
      </div>

      {isLoading && (
        <div className="text-center py-4 text-gray-500">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-400 mx-auto"></div>
          <p className="mt-2 text-xs">Loading invitations...</p>
        </div>
      )}

      {!isLoading && invitations.length === 0 && (
        <div className="text-center py-6 text-gray-500">
          <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-xs">No pending invitations</p>
        </div>
      )}

      {invitations.map((invitation) => {
        const isProcessing = processingIds.has(invitation.id);

        return (
          <Card key={invitation.id} className="bg-gray-800 border-gray-700">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  {invitation.channel.isPrivate ? (
                    <Lock className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Hash className="h-4 w-4 text-gray-400" />
                  )}
                  <CardTitle className="text-sm text-white">
                    #{invitation.channel.name}
                  </CardTitle>
                </div>
                <Badge variant="outline" className="text-xs text-gray-400">
                  {formatDate(invitation.createdAt)}
                </Badge>
              </div>
              {invitation.channel.description && (
                <CardDescription className="text-xs text-gray-500">
                  {invitation.channel.description}
                </CardDescription>
              )}
            </CardHeader>

            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Avatar className="h-6 w-6">
                    <AvatarFallback className="text-xs bg-gray-600">
                      {getUserInitials(invitation.inviter.username)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-xs text-gray-300">
                    Invited by {invitation.inviter.username}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleInvitationResponse(invitation.id, 'decline')}
                    disabled={isProcessing}
                    className="h-7 px-2 text-xs border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                  >
                    {isProcessing ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current" />
                    ) : (
                      <>
                        <XCircle className="h-3 w-3 mr-1" />
                        Decline
                      </>
                    )}
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => handleInvitationResponse(invitation.id, 'accept')}
                    disabled={isProcessing}
                    className="h-7 px-2 text-xs bg-green-600 hover:bg-green-700"
                  >
                    {isProcessing ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white" />
                    ) : (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Accept
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default InvitationsPanel;

import { Server as SocketIOServer } from 'socket.io';

let socketInstance: SocketIOServer | null = null;

export const setSocketInstance = (io: SocketIOServer) => {
  socketInstance = io;
};

export const getSocketInstance = (): SocketIOServer => {
  if (!socketInstance) {
    throw new Error('Socket.IO instance not initialized');
  }
  return socketInstance;
};

// Helper functions for emitting channel events
export const emitChannelCreated = (channel: any, createdBy: { userId: string; username: string }) => {
  const io = getSocketInstance();
  
  // Broadcast new public channel to all users
  if (!channel.isPrivate) {
    io.emit('newPublicChannel', {
      channel: {
        id: channel.id,
        name: channel.name,
        description: channel.description,
        isPrivate: channel.isPrivate,
        createdAt: channel.createdAt,
        updatedAt: channel.updatedAt,
        memberCount: channel._count?.members || 1
      },
      createdBy
    });
  }
};

export const emitChannelUpdated = (channelId: string, updatedBy: { userId: string; username: string }) => {
  const io = getSocketInstance();
  
  // Emit to a specific user's socket to trigger the broadcast
  io.emit('triggerChannelUpdate', {
    channelId,
    updatedBy
  });
};

export const emitChannelMembershipChanged = (
  channelId: string, 
  userId: string, 
  username: string, 
  action: 'joined' | 'left'
) => {
  const io = getSocketInstance();
  
  // Emit to a specific user's socket to trigger the broadcast
  io.emit('triggerMembershipChange', {
    channelId,
    userId,
    username,
    action
  });
};

export const emitChannelDeleted = (channelId: string, channelName: string, deletedBy: { userId: string; username: string }) => {
  const io = getSocketInstance();
  
  // Notify all users about channel deletion
  io.emit('channelDeleted', {
    channelId,
    channelName,
    deletedBy
  });
};
